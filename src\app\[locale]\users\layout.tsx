'use client'

import { use } from 'react'
import { ModernDashboardLayout } from '@/components/layout/modern-dashboard-layout'

export default function UsersLayoutWrapper({
  children,
  params
}: {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}) {
  const { locale } = use(params)
  return (
    <ModernDashboardLayout locale={locale}>
      {children}
    </ModernDashboardLayout>
  )
}
