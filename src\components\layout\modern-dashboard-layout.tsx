'use client'

import { useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import { useUser } from '@/components/auth/user-provider'
import { Button } from '@/components/ui/button'
import { LanguageSwitcher } from './language-switcher'
import { SidebarNavigation } from './sidebar-navigation'
import {
  BellIcon,
  UserCircleIcon,
  Bars3Icon
} from '@heroicons/react/24/outline'

interface ModernDashboardLayoutProps {
  children: React.ReactNode
  locale: string
}

export function ModernDashboardLayout({ children, locale }: ModernDashboardLayoutProps) {
  const t = useTranslations()
  const { user, signOut, loading } = useUser()
  const router = useRouter()
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const handleSignOut = async () => {
    await signOut()
    router.push(`/${locale}/auth/signin`)
  }

  // Handle redirect for unauthenticated users in useEffect
  useEffect(() => {
    if (!loading && !user) {
      router.push(`/${locale}/auth/signin`)
    }
  }, [loading, user, router, locale])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-lg text-gray-600">{t('common.loading')}</div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-lg text-gray-600">{t('common.loading')}</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar Navigation */}
      <SidebarNavigation
        locale={locale}
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* Main content */}
      <div className="lg:pl-64 flex flex-col flex-1">
        {/* Top header */}
        <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-gray-700 lg:hidden"
            onClick={() => setSidebarOpen(true)}
            aria-label="Open sidebar"
          >
            <Bars3Icon className="h-6 w-6" />
          </button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex flex-1"></div>
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* Notifications */}
              <button
                className="p-2 text-gray-400 hover:text-gray-500"
                aria-label="View notifications"
              >
                <BellIcon className="h-6 w-6" />
              </button>

              {/* Language switcher */}
              <LanguageSwitcher />

              {/* Profile dropdown */}
              <div className="flex items-center gap-x-2">
                <div className="hidden lg:flex lg:flex-col lg:items-end lg:leading-6">
                  <div className="text-sm font-semibold text-gray-900">
                    {user.full_name || user.email}
                  </div>
                  <div className="text-xs text-gray-500">Admin</div>
                </div>
                <UserCircleIcon className="h-8 w-8 text-gray-400" />
                <Button variant="outline" size="sm" onClick={handleSignOut} className="ml-2">
                  {t('auth.signOut')}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1">
          <div className="px-4 py-6 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
