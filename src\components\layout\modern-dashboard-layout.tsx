'use client'

import { useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { useUser } from '@/components/auth/user-provider'
import { Button } from '@/components/ui/button'
import { LanguageSwitcher } from './language-switcher'
import {
  HomeIcon,
  BuildingOfficeIcon,
  BuildingOffice2Icon,
  UsersIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  EnvelopeIcon,
  BoltIcon,
  ChartPieIcon,
  MagnifyingGlassIcon,
  BellIcon,
  UserCircleIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline'

interface ModernDashboardLayoutProps {
  children: React.ReactNode
  locale: string
}

export function ModernDashboardLayout({ children, locale }: ModernDashboardLayoutProps) {
  const t = useTranslations()
  const { user, signOut, loading } = useUser()
  const pathname = usePathname()
  const router = useRouter()
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const handleSignOut = async () => {
    await signOut()
    router.push(`/${locale}/auth/signin`)
  }

  // Handle redirect for unauthenticated users in useEffect
  useEffect(() => {
    if (!loading && !user) {
      router.push(`/${locale}/auth/signin`)
    }
  }, [loading, user, router, locale])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-lg text-gray-600">{t('common.loading')}</div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-lg text-gray-600">{t('common.loading')}</div>
      </div>
    )
  }

  const navigation = [
    { 
      name: t('navigation.dashboard'), 
      href: `/${locale}/dashboard`, 
      icon: HomeIcon,
      current: pathname === `/${locale}/dashboard`
    },
    { 
      name: t('navigation.organizations'), 
      href: `/${locale}/organizations`, 
      icon: BuildingOfficeIcon,
      current: pathname.startsWith(`/${locale}/organizations`)
    },
    { 
      name: t('navigation.facilities'), 
      href: `/${locale}/facilities`, 
      icon: BuildingOffice2Icon,
      current: pathname.startsWith(`/${locale}/facilities`)
    },
    { 
      name: t('navigation.users'), 
      href: `/${locale}/users`, 
      icon: UsersIcon,
      current: pathname.startsWith(`/${locale}/users`)
    },
  ]

  const tools = [
    { name: 'Email', icon: EnvelopeIcon, href: '#' },
    { name: 'Automation', icon: BoltIcon, href: '#' },
    { name: 'Analytics', icon: ChartPieIcon, href: '#' },
  ]

  const workspace = [
    { name: 'Campaign', icon: ChartBarIcon, href: '#', count: 5 },
    { name: 'Product Plan', icon: Cog6ToothIcon, href: '#', count: 4 },
  ]

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-gray-900">
          <div className="flex h-16 shrink-0 items-center justify-between px-6">
            <div className="flex items-center">
              <div className="flex h-8 w-8 items-center justify-center rounded bg-orange-500 text-white font-bold text-sm">
                UV
              </div>
              <div className="ml-3">
                <div className="text-white font-semibold text-sm">Userflow Inc.</div>
                <div className="text-gray-400 text-xs">Free Plan</div>
              </div>
            </div>
            <button onClick={() => setSidebarOpen(false)} className="text-gray-400 hover:text-white">
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          {/* Mobile navigation content */}
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-40 lg:flex lg:w-64 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900 px-6 pb-4">
          {/* Logo */}
          <div className="flex h-16 shrink-0 items-center">
            <div className="flex items-center">
              <div className="flex h-8 w-8 items-center justify-center rounded bg-orange-500 text-white font-bold text-sm">
                UV
              </div>
              <div className="ml-3">
                <div className="text-white font-semibold text-sm">Userflow Inc.</div>
                <div className="text-gray-400 text-xs">Free Plan</div>
              </div>
            </div>
          </div>

          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search"
              className="block w-full pl-10 pr-3 py-2 border border-gray-700 rounded-md leading-5 bg-gray-800 text-gray-300 placeholder-gray-400 focus:outline-none focus:bg-gray-700 focus:border-gray-600 focus:ring-1 focus:ring-gray-600 text-sm"
            />
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <span className="text-gray-400 text-xs">⌘ K</span>
            </div>
          </div>

          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              {/* Main Menu */}
              <li>
                <div className="text-xs font-semibold leading-6 text-gray-400 uppercase tracking-wide">
                  MAIN MENU
                </div>
                <ul role="list" className="mt-2 space-y-1">
                  {navigation.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className={`${
                          item.current
                            ? 'bg-gray-800 text-white'
                            : 'text-gray-300 hover:text-white hover:bg-gray-800'
                        } group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-medium`}
                      >
                        <item.icon className="h-5 w-5 shrink-0" />
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </li>

              {/* Tools */}
              <li>
                <div className="text-xs font-semibold leading-6 text-gray-400 uppercase tracking-wide">
                  TOOLS
                </div>
                <ul role="list" className="mt-2 space-y-1">
                  {tools.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="text-gray-300 hover:text-white hover:bg-gray-800 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-medium"
                      >
                        <item.icon className="h-5 w-5 shrink-0" />
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </li>

              {/* Workspace */}
              <li>
                <div className="text-xs font-semibold leading-6 text-gray-400 uppercase tracking-wide">
                  WORKSPACE
                </div>
                <ul role="list" className="mt-2 space-y-1">
                  {workspace.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="text-gray-300 hover:text-white hover:bg-gray-800 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-medium justify-between"
                      >
                        <div className="flex gap-x-3">
                          <item.icon className="h-5 w-5 shrink-0" />
                          {item.name}
                        </div>
                        <span className="bg-gray-700 text-gray-300 text-xs px-2 py-0.5 rounded-full">
                          {item.count}
                        </span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </li>

              {/* Bottom items */}
              <li className="mt-auto">
                <ul role="list" className="space-y-1">
                  <li>
                    <Link
                      href={`/${locale}/settings`}
                      className={`${
                        pathname.startsWith(`/${locale}/settings`)
                          ? 'bg-gray-800 text-white'
                          : 'text-gray-300 hover:text-white hover:bg-gray-800'
                      } group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-medium`}
                    >
                      <Cog6ToothIcon className="h-5 w-5 shrink-0" />
                      {t('navigation.settings')}
                    </Link>
                  </li>
                </ul>
              </li>
            </ul>
          </nav>

          {/* Upgrade section */}
          <div className="bg-gray-800 rounded-lg p-4 mt-4">
            <div className="flex items-center">
              <div className="bg-orange-500 rounded p-1">
                <BoltIcon className="h-4 w-4 text-white" />
              </div>
              <div className="ml-3 flex-1">
                <div className="text-white text-sm font-medium">Upgrade & unlock</div>
                <div className="text-gray-400 text-xs">all features</div>
              </div>
              <button className="text-gray-400 hover:text-white">
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64 flex flex-col flex-1">
        {/* Top header */}
        <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-gray-700 lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Bars3Icon className="h-6 w-6" />
          </button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex flex-1"></div>
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* Notifications */}
              <button className="p-2 text-gray-400 hover:text-gray-500">
                <BellIcon className="h-6 w-6" />
              </button>

              {/* Language switcher */}
              <LanguageSwitcher />

              {/* Profile dropdown */}
              <div className="flex items-center gap-x-2">
                <div className="hidden lg:flex lg:flex-col lg:items-end lg:leading-6">
                  <div className="text-sm font-semibold text-gray-900">
                    {user.full_name || user.email}
                  </div>
                  <div className="text-xs text-gray-500">Admin</div>
                </div>
                <UserCircleIcon className="h-8 w-8 text-gray-400" />
                <Button variant="outline" size="sm" onClick={handleSignOut} className="ml-2">
                  {t('auth.signOut')}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1">
          <div className="px-4 py-6 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
