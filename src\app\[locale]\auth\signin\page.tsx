'use client'

import { useState, use, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { authService } from '@/lib/auth'
import { useUser } from '@/components/auth/user-provider'

export default function SignInPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = use(params)
  const t = useTranslations('auth')
  const router = useRouter()
  const { user, loading: userLoading } = useUser()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  // Redirect to dashboard if user is already authenticated
  useEffect(() => {
    console.log('🔍 Sign-in redirect check:', { userLoading, user: user ? `${user.email} (${user.id})` : 'null' })
    if (!userLoading && user && user.id) {
      console.log('✅ User authenticated, redirecting to dashboard...')
      const timer = setTimeout(() => {
        router.push(`/${locale}/dashboard`)
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [user, userLoading, router, locale])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      console.log('🔐 Attempting sign in...')
      await authService.signIn(email, password)
      console.log('✅ Sign in successful, waiting for auth state to update...')

      // Reset loading state - the useEffect will handle redirect when user state updates
      setLoading(false)

    } catch (err: any) {
      console.error('❌ Sign in error:', err)
      setError(err.message || t('invalidCredentials'))
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {t('signIn')}
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="email" className="sr-only">
                {t('email')}
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                placeholder={t('email')}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                {t('password')}
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                placeholder={t('password')}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <Link
              href={`/${locale}/auth/forgot-password`}
              className="text-sm text-indigo-600 hover:text-indigo-500"
            >
              {t('forgotPassword')}
            </Link>
          </div>

          <div>
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {loading ? t('loading') : t('signIn')}
            </button>
          </div>

          <div className="text-center">
            <span className="text-sm text-gray-600">
              {t('dontHaveAccount')}{' '}
              <Link
                href={`/${locale}/auth/signup`}
                className="font-medium text-indigo-600 hover:text-indigo-500"
              >
                {t('signUp')}
              </Link>
            </span>
          </div>
        </form>
      </div>
    </div>
  )
}
