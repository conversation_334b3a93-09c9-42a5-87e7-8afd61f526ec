'use client'

import { useState, useEffect, use } from 'react'
import { useTranslations } from 'next-intl'
import { useUser } from '@/components/auth/user-provider'
import { Button } from '@/components/ui/button'
import { DataTable, type Column } from '@/components/ui/DataTable'
import { StatusBadge } from '@/components/ui/StatusBadge'
import { ActionMenu, type ActionMenuItem } from '@/components/ui/ActionMenu'
import { supabase, type Organization, type Facility, type Workplace, type User } from '@/lib/supabase'
import toast from 'react-hot-toast'
import { PencilIcon, KeyIcon, UserIcon, UserPlusIcon, UserMinusIcon, BuildingOfficeIcon, MapPinIcon, TrashIcon } from '@heroicons/react/24/outline'

export default function SettingsPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = use(params)
  const t = useTranslations()
  const { user } = useUser()
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [facilities, setFacilities] = useState<Facility[]>([])
  const [workplaces, setWorkplaces] = useState<Workplace[]>([])
  const [users, setUsers] = useState<(User & { organization?: Organization })[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'profile' | 'organizations' | 'facilities' | 'workplaces' | 'users'>('profile')
  const [selectedFacility, setSelectedFacility] = useState<string>('')
  const [showCreateWorkplace, setShowCreateWorkplace] = useState(false)
  const [newWorkplace, setNewWorkplace] = useState({
    name: '',
    description: '',
    location: '',
  })
  const [creating, setCreating] = useState(false)
  const [editingWorkplace, setEditingWorkplace] = useState<Workplace | null>(null)
  const [showEditForm, setShowEditForm] = useState(false)
  const [updating, setUpdating] = useState(false)
  const [deleting, setDeleting] = useState<string | null>(null)

  // User management state
  const [showCreateUser, setShowCreateUser] = useState(false)
  const [newUser, setNewUser] = useState({
    email: '',
    full_name: '',
    account_number: '',
    job_duties: '',
    organization_id: '',
  })
  const [creatingUser, setCreatingUser] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [showEditUser, setShowEditUser] = useState(false)
  const [updatingUser, setUpdatingUser] = useState(false)
  const [temporaryPassword, setTemporaryPassword] = useState('')

  useEffect(() => {
    if (user) {
      fetchData()
    }
  }, [user])

  const fetchData = async () => {
    try {
      // Fetch organizations
      const { data: orgsData, error: orgsError } = await supabase
        .from('organizations')
        .select('*')
        .order('name')

      if (orgsError && orgsError.code !== '42P01') {
        throw orgsError
      }
      setOrganizations(orgsData || [])

      // Fetch facilities
      const { data: facilitiesData, error: facilitiesError } = await supabase
        .from('facilities')
        .select(`
          *,
          organization:organizations(name)
        `)
        .order('created_at', { ascending: false })

      if (facilitiesError && facilitiesError.code !== '42P01') {
        throw facilitiesError
      }
      setFacilities(facilitiesData || [])

      // Fetch workplaces
      const { data: workplacesData, error: workplacesError } = await supabase
        .from('workplaces')
        .select(`
          *,
          facility:facilities(name, organization_id)
        `)
        .order('created_at', { ascending: false })

      if (workplacesError && workplacesError.code !== '42P01') {
        throw workplacesError
      }
      setWorkplaces(workplacesData || [])

      // Fetch users in user's organizations
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select(`
          *,
          user_roles!inner(
            organization_id,
            organizations(name)
          )
        `)
        .order('created_at', { ascending: false })

      if (usersError && usersError.code !== '42P01') {
        throw usersError
      }

      // Transform users data to include organization info
      const transformedUsers = (usersData || []).map(user => ({
        ...user,
        organization: (user as any).user_roles?.[0]?.organizations
      }))
      setUsers(transformedUsers)
    } catch (error) {
      console.warn('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const createWorkplace = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !newWorkplace.name.trim() || !selectedFacility) {
      toast.error('Please fill in all required fields and select a facility.')
      return
    }

    setCreating(true)
    try {
      const { error } = await supabase
        .from('workplaces')
        .insert({
          name: newWorkplace.name.trim(),
          description: newWorkplace.description.trim() || null,
          location: newWorkplace.location.trim() || null,
          facility_id: selectedFacility,
          created_by: user.id,
        })

      if (error) {
        if (error.code === '42P01' || error.message.includes('relation') || error.message.includes('does not exist')) {
          toast.error('Database setup required. Please run the database setup script first. See DATABASE_SETUP.md for instructions.')
          setCreating(false)
          return
        }
        throw error
      }

      // Refresh data
      await fetchData()

      // Reset form
      setNewWorkplace({
        name: '',
        description: '',
        location: '',
      })
      setSelectedFacility('')
      setShowCreateWorkplace(false)

      toast.success('Workplace created successfully!')
    } catch (error: any) {
      console.warn('Error creating workplace:', error)
      toast.error(`Error creating workplace: ${error.message || 'Please try again.'}`)
    } finally {
      setCreating(false)
    }
  }

  const updateWorkplace = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !editingWorkplace || !editingWorkplace.name.trim()) {
      toast.error('Please fill in all required fields.')
      return
    }

    setUpdating(true)
    try {
      const { error } = await supabase
        .from('workplaces')
        .update({
          name: editingWorkplace.name.trim(),
          description: editingWorkplace.description?.trim() || null,
          location: editingWorkplace.location?.trim() || null,
        })
        .eq('id', editingWorkplace.id)

      if (error) {
        if (error.code === '42P01' || error.message.includes('relation') || error.message.includes('does not exist')) {
          toast.error('Database setup required. Please run the database setup script first.')
          setUpdating(false)
          return
        }
        throw error
      }

      // Refresh data
      await fetchData()

      // Reset form
      setEditingWorkplace(null)
      setShowEditForm(false)

      toast.success('Workplace updated successfully!')
    } catch (error: any) {
      console.warn('Error updating workplace:', error)
      toast.error(`Error updating workplace: ${error.message || 'Please try again.'}`)
    } finally {
      setUpdating(false)
    }
  }

  const deleteWorkplace = async (workplaceId: string, workplaceName: string) => {
    if (!confirm(`Are you sure you want to delete "${workplaceName}"? This action cannot be undone.`)) {
      return
    }

    setDeleting(workplaceId)
    try {
      const { error } = await supabase
        .from('workplaces')
        .delete()
        .eq('id', workplaceId)

      if (error) {
        if (error.code === '42P01' || error.message.includes('relation') || error.message.includes('does not exist')) {
          toast.error('Database setup required. Please run the database setup script first.')
          setDeleting(null)
          return
        }
        throw error
      }

      // Refresh data
      await fetchData()

      toast.success('Workplace deleted successfully!')
    } catch (error: any) {
      console.warn('Error deleting workplace:', error)
      toast.error(`Error deleting workplace: ${error.message || 'Please try again.'}`)
    } finally {
      setDeleting(null)
    }
  }

  // User management functions
  const generatePassword = () => {
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789'
    let password = ''
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return password
  }

  const createUser = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !newUser.email.trim() || !newUser.full_name.trim() || !newUser.organization_id) {
      toast.error('Please fill in all required fields.')
      return
    }

    setCreatingUser(true)

    try {
      // Get the current session token
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        throw new Error('Not authenticated')
      }

      // Call the API route to create user
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          email: newUser.email.trim(),
          full_name: newUser.full_name.trim(),
          account_number: newUser.account_number.trim() || null,
          job_duties: newUser.job_duties.trim() || null,
          organization_id: newUser.organization_id,
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create user')
      }

      // Set the temporary password for display
      setTemporaryPassword(result.temporaryPassword)

      // Refresh data
      await fetchData()

      // Reset form
      setNewUser({
        email: '',
        full_name: '',
        account_number: '',
        job_duties: '',
        organization_id: '',
      })

      toast.success('User created successfully!')
    } catch (error: any) {
      console.warn('Error creating user:', error)
      toast.error(`Error creating user: ${error.message || 'Please try again.'}`)
      setTemporaryPassword('')
    } finally {
      setCreatingUser(false)
    }
  }

  const updateUser = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !editingUser || !editingUser.full_name?.trim()) {
      toast.error('Please fill in all required fields.')
      return
    }

    setUpdatingUser(true)
    try {
      const { error } = await supabase
        .from('users')
        .update({
          full_name: editingUser.full_name.trim(),
          account_number: editingUser.account_number?.trim() || null,
          job_duties: editingUser.job_duties?.trim() || null,
        })
        .eq('id', editingUser.id)

      if (error) {
        throw error
      }

      // Refresh data
      await fetchData()

      // Reset form
      setEditingUser(null)
      setShowEditUser(false)

      toast.success('User updated successfully!')
    } catch (error: any) {
      console.warn('Error updating user:', error)
      toast.error(`Error updating user: ${error.message || 'Please try again.'}`)
    } finally {
      setUpdatingUser(false)
    }
  }

  const toggleUserStatus = async (userId: string, currentStatus: boolean, userName: string) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ is_active: !currentStatus })
        .eq('id', userId)

      if (error) {
        throw error
      }

      // Refresh data
      await fetchData()

      toast.success(`User ${!currentStatus ? 'enabled' : 'disabled'} successfully!`)
    } catch (error: any) {
      console.warn('Error updating user status:', error)
      toast.error(`Error updating user status: ${error.message || 'Please try again.'}`)
    }
  }

  const resetUserPassword = async (userId: string, userEmail: string) => {
    if (!confirm(`Are you sure you want to reset the password for ${userEmail}?`)) {
      return
    }

    try {
      const newPassword = generatePassword()

      // Get the current session token
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        throw new Error('Not authenticated')
      }

      // Call the API route to reset password
      const response = await fetch('/api/admin/users', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          userId: userId,
          password: newPassword
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to reset password')
      }

      setTemporaryPassword(newPassword)
      toast.success('Password reset successfully! New password generated.')
    } catch (error: any) {
      console.warn('Error resetting password:', error)
      toast.error(`Error resetting password: ${error.message || 'Please try again.'}`)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast.success('Password copied to clipboard!')
    } catch (error) {
      toast.error('Failed to copy password')
    }
  }

  if (loading) {
    return <div className="text-center py-8">{t('common.loading')}</div>
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {t('settings.title')}
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            Manage your account and application settings.
          </p>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'profile', name: t('navigation.profile') },
            { id: 'organizations', name: t('settings.organizationSettings') },
            { id: 'facilities', name: t('settings.facilitySettings') },
            { id: 'workplaces', name: t('settings.workplaceSettings') },
            { id: 'users', name: t('users.userManagement') },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`${
                activeTab === tab.id
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`}
            >
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'profile' && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">
              {t('navigation.profile')}
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  {t('auth.email')}
                </label>
                <p className="mt-1 text-sm text-gray-900">{user?.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  {t('auth.fullName')}
                </label>
                <p className="mt-1 text-sm text-gray-900">{user?.full_name || 'Not set'}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'organizations' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900">
              {t('settings.organizationSettings')}
            </h2>
          </div>

          <DataTable
            data={organizations}
            columns={[
              {
                key: 'name',
                header: t('organizations.organizationName'),
                sortable: true,
                render: (org) => (
                  <div className="text-sm font-medium text-gray-900">{org.name}</div>
                )
              },
              {
                key: 'description',
                header: t('organizations.description'),
                sortable: true,
                render: (org) => (
                  <div className="text-sm text-gray-600 max-w-xs truncate">
                    {org.description || '-'}
                  </div>
                )
              },
              {
                key: 'created_at',
                header: t('common.createdDate'),
                sortable: true,
                render: (org) => (
                  <div className="text-sm text-gray-500">
                    {new Date(org.created_at).toLocaleDateString()}
                  </div>
                )
              },
              {
                key: 'facilities_count',
                header: t('facilities.facilitiesCount'),
                sortable: true,
                render: (org) => {
                  const count = facilities.filter(f => f.organization_id === org.id).length
                  return <div className="text-sm text-gray-600">{count}</div>
                }
              },
              {
                key: 'actions',
                header: t('common.actions'),
                render: (org) => (
                  <ActionMenu
                    items={[
                      {
                        label: t('common.edit'),
                        icon: <PencilIcon className="w-4 h-4" />,
                        onClick: () => {
                          // TODO: Implement edit organization
                          toast.error('Edit organization functionality coming soon')
                        }
                      }
                    ]}
                  />
                )
              }
            ] as Column<typeof organizations[0]>[]}
            searchable={true}
            searchPlaceholder={`${t('common.search')} organizations...`}
            emptyMessage="No organizations found. Create an organization first."
            loading={loading}
            pageSize={10}
          />
        </div>
      )}

      {activeTab === 'facilities' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900">
              {t('settings.facilitySettings')}
            </h2>
          </div>

          <DataTable
            data={facilities}
            columns={[
              {
                key: 'name',
                header: t('facilities.facilityName'),
                sortable: true,
                render: (facility) => (
                  <div className="flex items-center">
                    <BuildingOfficeIcon className="w-5 h-5 text-gray-400 mr-2" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">{facility.name}</div>
                      <div className="text-sm text-gray-500">
                        {(facility as any).organization?.name}
                      </div>
                    </div>
                  </div>
                )
              },
              {
                key: 'description',
                header: t('facilities.description'),
                sortable: true,
                render: (facility) => (
                  <div className="text-sm text-gray-600 max-w-xs truncate">
                    {facility.description || '-'}
                  </div>
                )
              },
              {
                key: 'address',
                header: t('facilities.address'),
                sortable: true,
                render: (facility) => (
                  <div className="flex items-center text-sm text-gray-600">
                    {facility.address ? (
                      <>
                        <MapPinIcon className="w-4 h-4 text-gray-400 mr-1" />
                        <span className="max-w-xs truncate">{facility.address}</span>
                      </>
                    ) : (
                      '-'
                    )}
                  </div>
                )
              },
              {
                key: 'created_at',
                header: t('common.createdDate'),
                sortable: true,
                render: (facility) => (
                  <div className="text-sm text-gray-500">
                    {new Date(facility.created_at).toLocaleDateString()}
                  </div>
                )
              },
              {
                key: 'actions',
                header: t('common.actions'),
                render: (facility) => (
                  <ActionMenu
                    items={[
                      {
                        label: t('common.edit'),
                        icon: <PencilIcon className="w-4 h-4" />,
                        onClick: () => {
                          // TODO: Implement edit facility
                          toast.error('Edit facility functionality coming soon')
                        }
                      },
                      {
                        label: t('workplaces.manageWorkplaces'),
                        icon: <BuildingOfficeIcon className="w-4 h-4" />,
                        onClick: () => {
                          setSelectedFacility(facility.id)
                          setActiveTab('workplaces')
                        }
                      }
                    ]}
                  />
                )
              }
            ] as Column<typeof facilities[0]>[]}
            searchable={true}
            searchPlaceholder={`${t('common.search')} facilities...`}
            emptyMessage="No facilities found. Create facilities through organization details pages."
            loading={loading}
            pageSize={10}
          />
        </div>
      )}

      {activeTab === 'workplaces' && (
        <div className="space-y-6">
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-medium text-gray-900">
                  {t('settings.workplaceSettings')}
                </h2>
                <Button onClick={() => setShowCreateWorkplace(true)}>
                  {t('workplaces.createWorkplace')}
                </Button>
              </div>

              {/* Create Workplace Form */}
              {showCreateWorkplace && (
                <div className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
                  <h3 className="text-md font-medium text-gray-900 mb-4">
                    {t('workplaces.createWorkplace')}
                  </h3>
                  <form onSubmit={createWorkplace} className="space-y-4">
                    <div>
                      <label htmlFor="facilitySelect" className="block text-sm font-medium text-gray-700">
                        Select Facility
                      </label>
                      <select
                        id="facilitySelect"
                        required
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        value={selectedFacility}
                        onChange={(e) => setSelectedFacility(e.target.value)}
                      >
                        <option value="">Select a facility</option>
                        {facilities.map((facility) => (
                          <option key={facility.id} value={facility.id}>
                            {facility.name} - {(facility as any).organization?.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label htmlFor="workplaceName" className="block text-sm font-medium text-gray-700">
                        {t('workplaces.workplaceName')}
                      </label>
                      <input
                        type="text"
                        id="workplaceName"
                        required
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        value={newWorkplace.name}
                        onChange={(e) => setNewWorkplace({ ...newWorkplace, name: e.target.value })}
                      />
                    </div>
                    <div>
                      <label htmlFor="workplaceDescription" className="block text-sm font-medium text-gray-700">
                        {t('facilities.description')}
                      </label>
                      <textarea
                        id="workplaceDescription"
                        rows={3}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        value={newWorkplace.description}
                        onChange={(e) => setNewWorkplace({ ...newWorkplace, description: e.target.value })}
                      />
                    </div>
                    <div>
                      <label htmlFor="workplaceLocation" className="block text-sm font-medium text-gray-700">
                        {t('workplaces.location')}
                      </label>
                      <input
                        type="text"
                        id="workplaceLocation"
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        value={newWorkplace.location}
                        onChange={(e) => setNewWorkplace({ ...newWorkplace, location: e.target.value })}
                      />
                    </div>
                    <div className="flex space-x-3">
                      <Button type="submit" disabled={creating}>
                        {creating ? t('common.loading') : t('common.create')}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setShowCreateWorkplace(false)
                          setNewWorkplace({ name: '', description: '', location: '' })
                          setSelectedFacility('')
                        }}
                      >
                        {t('common.cancel')}
                      </Button>
                    </div>
                  </form>
                </div>
              )}

              {/* Edit Workplace Form */}
              {showEditForm && editingWorkplace && (
                <div className="mb-6 p-4 border border-gray-200 rounded-lg bg-blue-50">
                  <h3 className="text-md font-medium text-gray-900 mb-4">
                    Edit Workplace
                  </h3>
                  <form onSubmit={updateWorkplace} className="space-y-4">
                    <div>
                      <label htmlFor="editWorkplaceName" className="block text-sm font-medium text-gray-700">
                        {t('workplaces.workplaceName')}
                      </label>
                      <input
                        type="text"
                        id="editWorkplaceName"
                        required
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        value={editingWorkplace.name}
                        onChange={(e) => setEditingWorkplace({ ...editingWorkplace, name: e.target.value })}
                      />
                    </div>
                    <div>
                      <label htmlFor="editWorkplaceDescription" className="block text-sm font-medium text-gray-700">
                        {t('facilities.description')}
                      </label>
                      <textarea
                        id="editWorkplaceDescription"
                        rows={3}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        value={editingWorkplace.description || ''}
                        onChange={(e) => setEditingWorkplace({ ...editingWorkplace, description: e.target.value })}
                      />
                    </div>
                    <div>
                      <label htmlFor="editWorkplaceLocation" className="block text-sm font-medium text-gray-700">
                        {t('workplaces.location')}
                      </label>
                      <input
                        type="text"
                        id="editWorkplaceLocation"
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        value={editingWorkplace.location || ''}
                        onChange={(e) => setEditingWorkplace({ ...editingWorkplace, location: e.target.value })}
                      />
                    </div>
                    <div className="flex space-x-3">
                      <Button type="submit" disabled={updating}>
                        {updating ? t('common.loading') : 'Update'}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setShowEditForm(false)
                          setEditingWorkplace(null)
                        }}
                      >
                        {t('common.cancel')}
                      </Button>
                    </div>
                  </form>
                </div>
              )}

              {/* Workplaces Table */}
              <DataTable
                data={workplaces.filter(workplace => !selectedFacility || workplace.facility_id === selectedFacility)}
                columns={[
                  {
                    key: 'name',
                    header: t('workplaces.workplaceName'),
                    sortable: true,
                    render: (workplace) => (
                      <div className="flex items-center">
                        <MapPinIcon className="w-5 h-5 text-gray-400 mr-2" />
                        <div>
                          <div className="text-sm font-medium text-gray-900">{workplace.name}</div>
                          <div className="text-sm text-gray-500">
                            {(workplace as any).facility?.name}
                          </div>
                        </div>
                      </div>
                    )
                  },
                  {
                    key: 'description',
                    header: t('workplaces.description'),
                    sortable: true,
                    render: (workplace) => (
                      <div className="text-sm text-gray-600 max-w-xs truncate">
                        {workplace.description || '-'}
                      </div>
                    )
                  },
                  {
                    key: 'location',
                    header: t('workplaces.location'),
                    sortable: true,
                    render: (workplace) => (
                      <div className="text-sm text-gray-600">
                        {workplace.location || '-'}
                      </div>
                    )
                  },
                  {
                    key: 'created_at',
                    header: t('common.createdDate'),
                    sortable: true,
                    render: (workplace) => (
                      <div className="text-sm text-gray-500">
                        {new Date(workplace.created_at).toLocaleDateString()}
                      </div>
                    )
                  },
                  {
                    key: 'actions',
                    header: t('common.actions'),
                    render: (workplace) => (
                      <ActionMenu
                        items={[
                          {
                            label: t('common.edit'),
                            icon: <PencilIcon className="w-4 h-4" />,
                            onClick: () => {
                              setEditingWorkplace(workplace)
                              setShowEditForm(true)
                              setShowCreateWorkplace(false)
                            }
                          },
                          {
                            label: t('common.delete'),
                            icon: <TrashIcon className="w-4 h-4" />,
                            onClick: () => deleteWorkplace(workplace.id, workplace.name),
                            variant: 'danger',
                            disabled: deleting === workplace.id
                          }
                        ]}
                      />
                    )
                  }
                ] as Column<typeof workplaces[0]>[]}
                searchable={true}
                searchPlaceholder={`${t('common.search')} workplaces...`}
                emptyMessage="No workplaces found. Create workplaces within your facilities."
                loading={loading}
                pageSize={10}
              />
            </div>
          </div>
        </div>
      )}

      {activeTab === 'users' && (
        <div className="space-y-6">
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-medium text-gray-900">
                  {t('users.userManagement')}
                </h2>
                <Button onClick={() => setShowCreateUser(true)}>
                  {t('users.createUser')}
                </Button>
              </div>

              {/* Temporary Password Display */}
              {temporaryPassword && (
                <div className="mb-6 p-4 border border-green-200 rounded-lg bg-green-50">
                  <h3 className="text-md font-medium text-green-900 mb-2">
                    {t('users.temporaryPassword')}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <code className="bg-white px-3 py-2 rounded border text-sm font-mono">
                      {temporaryPassword}
                    </code>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(temporaryPassword)}
                    >
                      {t('users.copyPassword')}
                    </Button>
                  </div>
                  <p className="text-sm text-green-700 mt-2">
                    Please share this password with the user securely. It will not be shown again.
                  </p>
                </div>
              )}

              {/* Create User Form */}
              {showCreateUser && (
                <div className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
                  <h3 className="text-md font-medium text-gray-900 mb-4">
                    {t('users.createUser')}
                  </h3>
                  <form onSubmit={createUser} className="space-y-4">
                    <div>
                      <label htmlFor="userOrganization" className="block text-sm font-medium text-gray-700">
                        {t('users.selectOrganization')} *
                      </label>
                      <select
                        id="userOrganization"
                        required
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        value={newUser.organization_id}
                        onChange={(e) => setNewUser({ ...newUser, organization_id: e.target.value })}
                      >
                        <option value="">{t('users.selectOrganization')}</option>
                        {organizations.map((org) => (
                          <option key={org.id} value={org.id}>
                            {org.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label htmlFor="userEmail" className="block text-sm font-medium text-gray-700">
                          {t('users.userEmail')} *
                        </label>
                        <input
                          type="email"
                          id="userEmail"
                          required
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                          value={newUser.email}
                          onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                        />
                      </div>
                      <div>
                        <label htmlFor="userFullName" className="block text-sm font-medium text-gray-700">
                          {t('users.userFullName')} *
                        </label>
                        <input
                          type="text"
                          id="userFullName"
                          required
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                          value={newUser.full_name}
                          onChange={(e) => setNewUser({ ...newUser, full_name: e.target.value })}
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label htmlFor="userAccountNumber" className="block text-sm font-medium text-gray-700">
                          {t('users.accountNumber')}
                        </label>
                        <input
                          type="text"
                          id="userAccountNumber"
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                          value={newUser.account_number}
                          onChange={(e) => setNewUser({ ...newUser, account_number: e.target.value })}
                        />
                      </div>
                      <div>
                        <label htmlFor="userJobDuties" className="block text-sm font-medium text-gray-700">
                          {t('users.jobDuties')}
                        </label>
                        <input
                          type="text"
                          id="userJobDuties"
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                          value={newUser.job_duties}
                          onChange={(e) => setNewUser({ ...newUser, job_duties: e.target.value })}
                        />
                      </div>
                    </div>
                    <div className="flex space-x-3">
                      <Button type="submit" disabled={creatingUser}>
                        {creatingUser ? t('common.loading') : t('users.createUser')}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setShowCreateUser(false)
                          setNewUser({
                            email: '',
                            full_name: '',
                            account_number: '',
                            job_duties: '',
                            organization_id: '',
                          })
                          setTemporaryPassword('')
                        }}
                      >
                        {t('common.cancel')}
                      </Button>
                    </div>
                  </form>
                </div>
              )}

              {/* Edit User Form */}
              {showEditUser && editingUser && (
                <div className="mb-6 p-4 border border-gray-200 rounded-lg bg-blue-50">
                  <h3 className="text-md font-medium text-gray-900 mb-4">
                    {t('users.editUser')}
                  </h3>
                  <form onSubmit={updateUser} className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label htmlFor="editUserEmail" className="block text-sm font-medium text-gray-700">
                          {t('users.userEmail')}
                        </label>
                        <input
                          type="email"
                          id="editUserEmail"
                          disabled
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-100 text-gray-500"
                          value={editingUser.email}
                        />
                      </div>
                      <div>
                        <label htmlFor="editUserFullName" className="block text-sm font-medium text-gray-700">
                          {t('users.userFullName')} *
                        </label>
                        <input
                          type="text"
                          id="editUserFullName"
                          required
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                          value={editingUser.full_name || ''}
                          onChange={(e) => setEditingUser({ ...editingUser, full_name: e.target.value })}
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label htmlFor="editUserAccountNumber" className="block text-sm font-medium text-gray-700">
                          {t('users.accountNumber')}
                        </label>
                        <input
                          type="text"
                          id="editUserAccountNumber"
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                          value={editingUser.account_number || ''}
                          onChange={(e) => setEditingUser({ ...editingUser, account_number: e.target.value })}
                        />
                      </div>
                      <div>
                        <label htmlFor="editUserJobDuties" className="block text-sm font-medium text-gray-700">
                          {t('users.jobDuties')}
                        </label>
                        <input
                          type="text"
                          id="editUserJobDuties"
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                          value={editingUser.job_duties || ''}
                          onChange={(e) => setEditingUser({ ...editingUser, job_duties: e.target.value })}
                        />
                      </div>
                    </div>
                    <div className="flex space-x-3">
                      <Button type="submit" disabled={updatingUser}>
                        {updatingUser ? t('common.loading') : 'Update'}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setShowEditUser(false)
                          setEditingUser(null)
                        }}
                      >
                        {t('common.cancel')}
                      </Button>
                    </div>
                  </form>
                </div>
              )}

              {/* Users Table */}
              <DataTable
                data={users}
                columns={[
                  {
                    key: 'full_name',
                    header: t('users.userFullName'),
                    sortable: true,
                    render: (user) => (
                      <div className="flex items-center">
                        <UserIcon className="w-5 h-5 text-gray-400 mr-2" />
                        <div>
                          <div className="text-sm font-medium text-gray-900">{user.full_name}</div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </div>
                      </div>
                    )
                  },
                  {
                    key: 'account_number',
                    header: t('users.accountNumber'),
                    sortable: true,
                    render: (user) => user.account_number || '-'
                  },
                  {
                    key: 'job_duties',
                    header: t('users.jobDuties'),
                    sortable: true,
                    render: (user) => user.job_duties || '-'
                  },
                  {
                    key: 'organization',
                    header: t('users.organization'),
                    sortable: true,
                    render: (user) => user.organization?.name || '-'
                  },
                  {
                    key: 'is_active',
                    header: t('users.status'),
                    sortable: true,
                    render: (user) => (
                      <StatusBadge status={user.is_active ? 'active' : 'inactive'}>
                        {user.is_active ? t('users.active') : t('users.inactive')}
                      </StatusBadge>
                    )
                  },
                  {
                    key: 'actions',
                    header: t('common.actions'),
                    render: (user) => (
                      <ActionMenu
                        items={[
                          {
                            label: t('common.edit'),
                            icon: <PencilIcon className="w-4 h-4" />,
                            onClick: () => {
                              setEditingUser(user)
                              setShowEditUser(true)
                              setShowCreateUser(false)
                            }
                          },
                          {
                            label: user.is_active ? t('users.disable') : t('users.enable'),
                            icon: user.is_active ? <UserMinusIcon className="w-4 h-4" /> : <UserPlusIcon className="w-4 h-4" />,
                            onClick: () => toggleUserStatus(user.id, user.is_active || false, user.full_name || ''),
                            variant: user.is_active ? 'danger' : 'default'
                          },
                          {
                            label: t('users.resetPassword'),
                            icon: <KeyIcon className="w-4 h-4" />,
                            onClick: () => resetUserPassword(user.id, user.email)
                          }
                        ]}
                      />
                    )
                  }
                ] as Column<typeof users[0]>[]}
                searchable={true}
                searchPlaceholder={`${t('common.search')} users...`}
                emptyMessage={t('users.noUsers')}
                loading={loading}
                pageSize={10}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
