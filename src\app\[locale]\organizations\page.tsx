'use client'

import { useState, useEffect, use } from 'react'
import { useTranslations } from 'next-intl'
import { useUser } from '@/components/auth/user-provider'
import { Button } from '@/components/ui/button'
import { DataTable, Column } from '@/components/ui/data-table'
import { StatusBadge } from '@/components/ui/status-badge'
import { StatsGrid } from '@/components/ui/stats-cards'
import { supabase, type Organization } from '@/lib/supabase'
import { authService } from '@/lib/auth'
import Link from 'next/link'
import toast from 'react-hot-toast'
import {
  BuildingOfficeIcon,
  UsersIcon,
  BuildingOffice2Icon,
  ChartBarIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline'

export default function OrganizationsPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = use(params)
  const t = useTranslations()
  const { user } = useUser()
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newOrgName, setNewOrgName] = useState('')
  const [newOrgDescription, setNewOrgDescription] = useState('')
  const [creating, setCreating] = useState(false)

  useEffect(() => {
    if (user) {
      fetchOrganizations()
    }
  }, [user])

  const fetchOrganizations = async () => {
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        // Check if it's a table not found error
        if (error.code === '42P01' || error.message.includes('relation') || error.message.includes('does not exist')) {
          console.warn('Organizations table not found. Database setup required.')
          setOrganizations([])
          return
        }
        throw error
      }
      setOrganizations(data || [])
    } catch (error) {
      console.warn('Error fetching organizations:', error)
      setOrganizations([])
    } finally {
      setLoading(false)
    }
  }

  const createOrganization = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !newOrgName.trim()) return

    setCreating(true)
    try {
      // Create organization
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .insert({
          name: newOrgName.trim(),
          description: newOrgDescription.trim() || null,
          created_by: user.id,
        })
        .select()
        .single()

      if (orgError) {
        // Check if it's a table not found error
        if (orgError.code === '42P01' || orgError.message.includes('relation') || orgError.message.includes('does not exist')) {
          toast.error('Database setup required. Please run the database setup script first. See DATABASE_SETUP.md for instructions.')
          return
        }
        throw orgError
      }

      // Try to assign admin role to creator (optional if roles table exists)
      try {
        const { data: adminRole } = await supabase
          .from('roles')
          .select('id')
          .eq('name', 'Admin')
          .single()

        if (adminRole) {
          const { error: roleError } = await supabase
            .from('user_roles')
            .insert({
              user_id: user.id,
              organization_id: orgData.id,
              role_id: adminRole.id,
            })

          if (roleError) {
            console.warn('Could not assign role:', roleError.message)
          }
        }
      } catch (roleErr) {
        console.warn('Roles table not found, skipping role assignment')
      }

      // Refresh organizations list
      await fetchOrganizations()

      // Reset form
      setNewOrgName('')
      setNewOrgDescription('')
      setShowCreateForm(false)

      toast.success('Organization created successfully!')
    } catch (error: any) {
      console.warn('Error creating organization:', error)
      toast.error(`Error creating organization: ${error.message || 'Please try again.'}`)
    } finally {
      setCreating(false)
    }
  }

  if (loading) {
    return <div className="text-center py-8">{t('common.loading')}</div>
  }

  // Sample stats for organizations
  const stats = [
    {
      title: 'Total Organizations',
      value: organizations.length.toString(),
      change: { value: '+ 2 new', type: 'increase' as const, period: 'this month' },
      icon: BuildingOfficeIcon
    },
    {
      title: 'Active Users',
      value: '156',
      change: { value: '+ 12%', type: 'increase' as const, period: 'vs last month' },
      icon: UsersIcon
    },
    {
      title: 'Total Facilities',
      value: '24',
      change: { value: '+ 3', type: 'increase' as const, period: 'this month' },
      icon: BuildingOffice2Icon
    },
    {
      title: 'Monthly Growth',
      value: '8.2%',
      change: { value: '+ 2.1%', type: 'increase' as const, period: 'vs last month' },
      icon: ChartBarIcon
    }
  ]

  // Table columns for organizations
  const organizationColumns: Column[] = [
    {
      key: 'name',
      label: 'Organization Name',
      render: (value, row) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-lg bg-orange-100 flex items-center justify-center">
              <BuildingOfficeIcon className="h-6 w-6 text-orange-600" />
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{value}</div>
            <div className="text-sm text-gray-500">{row.description || 'No description'}</div>
          </div>
        </div>
      )
    },
    {
      key: 'created_at',
      label: 'Created',
      render: (value) => new Date(value).toLocaleDateString()
    },
    {
      key: 'status',
      label: 'Status',
      render: () => <StatusBadge status="Active" variant="success" />
    },
    {
      key: 'facilities_count',
      label: 'Facilities',
      render: () => Math.floor(Math.random() * 10) + 1 // Mock data
    },
    {
      key: 'users_count',
      label: 'Users',
      render: () => Math.floor(Math.random() * 50) + 5 // Mock data
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Organizations</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage your organizations and their settings.
          </p>
        </div>
      </div>

      {/* Statistics Cards */}
      <StatsGrid stats={stats} />

      {/* Create Organization Form */}
      {showCreateForm && (
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            {t('organizations.createOrganization')}
          </h2>
          <form onSubmit={createOrganization} className="space-y-4">
            <div>
              <label htmlFor="orgName" className="block text-sm font-medium text-gray-700">
                {t('organizations.organizationName')}
              </label>
              <input
                type="text"
                id="orgName"
                required
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                value={newOrgName}
                onChange={(e) => setNewOrgName(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="orgDescription" className="block text-sm font-medium text-gray-700">
                {t('organizations.description')}
              </label>
              <textarea
                id="orgDescription"
                rows={3}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                value={newOrgDescription}
                onChange={(e) => setNewOrgDescription(e.target.value)}
              />
            </div>
            <div className="flex space-x-3">
              <Button type="submit" disabled={creating}>
                {creating ? t('common.loading') : t('common.create')}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowCreateForm(false)
                  setNewOrgName('')
                  setNewOrgDescription('')
                }}
              >
                {t('common.cancel')}
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* Database Setup Notice */}
      {!loading && organizations.length === 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Database Setup Required
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  To create organizations and facilities, you need to set up the database tables first.
                  Please follow the instructions in <code className="bg-yellow-100 px-1 rounded">DATABASE_SETUP.md</code> to run the database setup script.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Organizations Table */}
      {organizations.length === 0 ? (
        <div className="bg-white shadow-sm rounded-lg border border-gray-200">
          <div className="px-6 py-12 text-center">
            <BuildingOfficeIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              {t('organizations.noOrganizations')}
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by creating your first organization.
            </p>
            <div className="mt-6">
              <Button onClick={() => setShowCreateForm(true)}>
                <PlusIcon className="h-4 w-4 mr-2" />
                {t('organizations.createOrganization')}
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <DataTable
          title="Organizations"
          columns={organizationColumns}
          data={organizations}
          searchable={true}
          filterable={true}
          sortable={true}
          selectable={true}
          actions={[
            {
              label: 'Add Organization',
              icon: PlusIcon,
              onClick: () => setShowCreateForm(true),
              variant: 'primary'
            }
          ]}
          rowActions={[
            {
              label: 'View',
              icon: EyeIcon,
              onClick: (row) => window.location.href = `/${locale}/organizations/${row.id}`
            },
            {
              label: 'Edit',
              icon: PencilIcon,
              onClick: (row) => console.log('Edit', row)
            },
            {
              label: 'Delete',
              icon: TrashIcon,
              onClick: (row) => console.log('Delete', row)
            }
          ]}
          pagination={{
            page: 1,
            pageSize: 10,
            total: organizations.length,
            onPageChange: (page) => console.log('Page change:', page),
            onPageSizeChange: (pageSize) => console.log('Page size change:', pageSize)
          }}
        />
      )}
    </div>
  )
}
