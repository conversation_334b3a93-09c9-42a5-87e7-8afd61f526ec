import { supabase } from './supabase'
import type { User } from './supabase'

export interface AuthUser extends User {
  userRoles?: Array<{
    organization_id: string
    facility_id?: string
    role: {
      name: string
      permissions: Array<{
        name: string
        resource: string
        action: string
      }>
    }
  }>
}

export const authService = {
  // Sign up with email and password
  async signUp(email: string, password: string, fullName?: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
      },
    })

    if (error) throw error

    // Create user profile now that database is set up
    if (data.user) {
      try {
        const { error: profileError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            email: data.user.email!,
            full_name: fullName,
          })

        if (profileError) {
          console.warn('Could not create user profile:', profileError.message)
        }
      } catch (profileErr) {
        console.warn('Error creating user profile:', profileErr)
      }
    }

    return data
  },

  // Sign in with email and password
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) throw error
    return data
  },

  // Sign out
  async signOut() {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  },

  // Reset password
  async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    })

    if (error) throw error
  },

  // Update password
  async updatePassword(password: string) {
    const { error } = await supabase.auth.updateUser({
      password,
    })

    if (error) throw error
  },

  // Get current user with roles and permissions
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) return null

      // Try to get user profile from database
      let userProfile = null
      try {
        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single()

        if (!profileError && profile) {
          userProfile = profile
        }
      } catch (profileErr) {
        console.warn('Could not fetch user profile:', profileErr)
      }

      // Try to get user roles
      let userRoles: any[] = []
      try {
        const { data: roles, error: rolesError } = await supabase
          .from('user_roles')
          .select(`
            organization_id,
            facility_id,
            role_id,
            roles!inner(name)
          `)
          .eq('user_id', user.id)

        if (!rolesError && roles) {
          userRoles = roles.map((ur: any) => ({
            organization_id: ur.organization_id,
            facility_id: ur.facility_id,
            role: {
              name: ur.roles?.name || 'Unknown',
              permissions: [] // We'll implement permissions later
            }
          }))
        }
      } catch (rolesErr) {
        console.warn('Could not fetch user roles:', rolesErr)
      }

      // Return user with profile and role info
      return {
        id: user.id,
        email: user.email!,
        full_name: userProfile?.full_name || user.user_metadata?.full_name || null,
        avatar_url: userProfile?.avatar_url || null,
        created_at: userProfile?.created_at || user.created_at,
        updated_at: userProfile?.updated_at || user.updated_at || user.created_at,
        userRoles
      }
    } catch (error) {
      console.warn('Error in getCurrentUser:', error)
      return null
    }
  },

  // Check if user has permission (simplified - based on role name for now)
  hasPermission(user: AuthUser | null, permission: string, organizationId?: string): boolean {
    if (!user || !user.userRoles) return false

    return user.userRoles.some(userRole => {
      // If organizationId is specified, check only roles in that organization
      if (organizationId && userRole.organization_id !== organizationId) {
        return false
      }

      // For now, we'll use role-based permissions
      const roleName = userRole.role.name.toLowerCase()

      // Admin has all permissions
      if (roleName === 'admin') return true

      // Manager has most permissions except user management
      if (roleName === 'manager') {
        return !permission.includes('delete_user') && !permission.includes('manage_roles')
      }

      // Staff has read permissions only
      if (roleName === 'staff') {
        return permission.includes('read') || permission.includes('view')
      }

      return false
    })
  },

  // Get user's organizations
  getUserOrganizations(user: AuthUser | null): string[] {
    if (!user || !user.userRoles) return []

    const organizationIds = user.userRoles.map(ur => ur.organization_id)
    return Array.from(new Set(organizationIds))
  },
}
