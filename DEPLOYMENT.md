# Deployment Guide

This guide covers deploying the Internal VE SaaS application to various platforms.

## 🚀 Vercel Deployment (Recommended)

Vercel is the recommended platform for deploying Next.js applications.

### Prerequisites
- GitHub account with the repository
- Vercel account (free tier available)

### Steps

1. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Sign in with your GitHub account
   - Click "New Project"
   - Import the `internal-ve` repository

2. **Configure Environment Variables**
   In the Vercel dashboard, add these environment variables:
   ```
   NEXT_PUBLIC_SUPABASE_URL=https://hdompiwbxzymopmdhwbq.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************._GkTn1BtWRz0T3fNlBNBm2c5ezw3gGc40bzn8tq8JRw
   ```

3. **Deploy**
   - Click "Deploy"
   - Vercel will automatically build and deploy your application
   - Your app will be available at `https://your-project-name.vercel.app`

### Custom Domain (Optional)
1. In Vercel dashboard, go to your project
2. Navigate to "Settings" → "Domains"
3. Add your custom domain
4. Follow the DNS configuration instructions

## 🌐 Netlify Deployment

### Steps

1. **Connect Repository**
   - Go to [netlify.com](https://netlify.com)
   - Click "New site from Git"
   - Connect your GitHub account
   - Select the `internal-ve` repository

2. **Build Settings**
   - Build command: `npm run build`
   - Publish directory: `.next`

3. **Environment Variables**
   Add the same environment variables as listed above in the Netlify dashboard.

## ☁️ AWS Amplify Deployment

### Steps

1. **Connect Repository**
   - Go to AWS Amplify console
   - Click "New app" → "Host web app"
   - Connect your GitHub repository

2. **Build Settings**
   Amplify will auto-detect Next.js settings, but you can customize:
   ```yaml
   version: 1
   frontend:
     phases:
       preBuild:
         commands:
           - npm ci
       build:
         commands:
           - npm run build
     artifacts:
       baseDirectory: .next
       files:
         - '**/*'
     cache:
       paths:
         - node_modules/**/*
   ```

3. **Environment Variables**
   Add the environment variables in the Amplify console.

## 🐳 Docker Deployment

### Dockerfile
Create a `Dockerfile` in the project root:

```dockerfile
FROM node:18-alpine AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci --only=production

FROM node:18-alpine AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV production
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs
EXPOSE 3000
ENV PORT 3000

CMD ["node", "server.js"]
```

### Docker Compose
Create a `docker-compose.yml`:

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_SUPABASE_URL=https://hdompiwbxzymopmdhwbq.supabase.co
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************._GkTn1BtWRz0T3fNlBNBm2c5ezw3gGc40bzn8tq8JRw
```

## 🔧 Production Considerations

### Performance Optimization
1. **Enable compression** in your hosting platform
2. **Configure CDN** for static assets
3. **Set up monitoring** with tools like Vercel Analytics or Google Analytics

### Security
1. **Use HTTPS** (automatically provided by most platforms)
2. **Set up proper CORS** in Supabase dashboard
3. **Configure security headers** in `next.config.js`

### Monitoring
1. **Error tracking** with Sentry or similar
2. **Performance monitoring** with Vercel Analytics
3. **Uptime monitoring** with services like UptimeRobot

### Backup Strategy
1. **Database backups** are handled by Supabase automatically
2. **Code backups** are in GitHub
3. **Consider additional backup strategies** for critical data

## 🌍 Domain and SSL

Most modern hosting platforms provide:
- Automatic SSL certificates
- Custom domain support
- Global CDN

### DNS Configuration
When using a custom domain:
1. Point your domain to the hosting platform
2. Configure DNS records as instructed
3. Wait for propagation (usually 24-48 hours)

## 📊 Analytics and Monitoring

### Recommended Tools
- **Vercel Analytics** (if using Vercel)
- **Google Analytics** for user tracking
- **Sentry** for error monitoring
- **LogRocket** for session replay

### Setup
Add analytics scripts to your `layout.tsx` or use Next.js built-in analytics support.

## 🔄 CI/CD Pipeline

### GitHub Actions (Optional)
Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
    - name: Install dependencies
      run: npm ci
    - name: Run tests
      run: npm test
    - name: Build
      run: npm run build
```

## 🚨 Troubleshooting

### Common Issues
1. **Build failures**: Check Node.js version compatibility
2. **Environment variables**: Ensure all required variables are set
3. **Database connection**: Verify Supabase configuration
4. **CORS errors**: Configure allowed origins in Supabase

### Support
- Check the hosting platform's documentation
- Review Next.js deployment guides
- Consult Supabase documentation for database issues

---

**Happy Deploying! 🚀**
