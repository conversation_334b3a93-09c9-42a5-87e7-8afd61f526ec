'use client'

import { use } from 'react'
import { useTranslations } from 'next-intl'
import { useUser } from '@/components/auth/user-provider'
import { Button } from '@/components/ui/button'
import { StatsGrid } from '@/components/ui/stats-cards'
import { DataTable, Column } from '@/components/ui/data-table'
import { StatusBadge } from '@/components/ui/status-badge'
import { Rating } from '@/components/ui/rating'
import Link from 'next/link'
import {
  CubeIcon,
  CurrencyDollarIcon,
  ShoppingCartIcon,
  ChartBarIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline'

export default function DashboardPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = use(params)
  const t = useTranslations()
  const { user } = useUser()

  // Sample data for the modern dashboard
  const stats = [
    {
      title: 'Total Product',
      value: '250',
      change: { value: '+ 3 pending', type: 'increase' as const, period: 'vs last month' },
      icon: CubeIcon
    },
    {
      title: 'Product Revenue',
      value: '$15,490',
      change: { value: '+ 5%', type: 'increase' as const, period: 'vs last month' },
      icon: CurrencyDollarIcon
    },
    {
      title: 'Product Sold',
      value: '2,355',
      change: { value: '+ 7%', type: 'increase' as const, period: 'vs last month' },
      icon: ShoppingCartIcon
    },
    {
      title: 'Avg. Monthly Sales',
      value: '890',
      change: { value: '+ 6%', type: 'increase' as const, period: 'vs last month' },
      icon: ChartBarIcon
    }
  ]

  const productColumns: Column[] = [
    {
      key: 'product',
      label: 'Product',
      render: (value, row) => (
        <div className="flex items-center">
          <div className={`w-3 h-3 rounded-full mr-3 ${row.highlight ? 'bg-orange-500' : 'bg-transparent'}`}></div>
          <span className="font-medium text-gray-900">{value}</span>
        </div>
      )
    },
    {
      key: 'price',
      label: 'Price',
      render: (value) => <span className="text-gray-900">${value}</span>
    },
    {
      key: 'sales',
      label: 'Sales',
      render: (value) => <span className="text-gray-600">{value} pcs</span>
    },
    {
      key: 'revenue',
      label: 'Revenue',
      render: (value) => <span className="font-medium text-gray-900">${value}</span>
    },
    {
      key: 'stock',
      label: 'Stock',
      render: (value) => <span className="text-gray-600">{value}</span>
    },
    {
      key: 'status',
      label: 'Status',
      render: (value) => <StatusBadge status={value} />
    },
    {
      key: 'rating',
      label: 'Rating',
      render: (value) => <Rating rating={value} size="sm" />
    }
  ]

  const productData = [
    {
      product: 'Userflow T-Shirt #10 - White',
      price: '1.35',
      sales: '471',
      revenue: '635.85',
      stock: '100',
      status: 'In Stock',
      rating: 5.0,
      highlight: false
    },
    {
      product: 'Userflow T-Shirt #10 - Black',
      price: '1.35',
      sales: '402',
      revenue: '544.05',
      stock: '0',
      status: 'Out of Stock',
      rating: 5.0,
      highlight: true
    },
    {
      product: 'Userflow T-Shirt #10 - White',
      price: '1.35',
      sales: '455',
      revenue: '645.25',
      stock: '20',
      status: 'Restock',
      rating: 4.9,
      highlight: false
    },
    {
      product: 'SmartHome Hub',
      price: '150',
      sales: '7',
      revenue: '1,050.00',
      stock: '12',
      status: 'In Stock',
      rating: 4.8,
      highlight: false
    },
    {
      product: 'UltraSound Wireless Earbuds',
      price: '200',
      sales: '5',
      revenue: '1,000.00',
      stock: '0',
      status: 'Out of Stock',
      rating: 4.8,
      highlight: false
    },
    {
      product: 'ProVision 4K Monitor',
      price: '400.25',
      sales: '1',
      revenue: '400.25',
      stock: '3',
      status: 'Restock',
      rating: 5.0,
      highlight: false
    },
    {
      product: 'Userflow Retro Wave Shirt',
      price: '1.35',
      sales: '120',
      revenue: '162.40',
      stock: '20',
      status: 'In Stock',
      rating: 4.7,
      highlight: false
    },
    {
      product: 'Userflow Graphic Art T-Shirt',
      price: '1.35',
      sales: '200',
      revenue: '270.15',
      stock: '0',
      status: 'Out of Stock',
      rating: 4.9,
      highlight: false
    },
    {
      product: 'Userflow Classic Fit Crewneck',
      price: '28.5',
      sales: '130',
      revenue: '3,705.25',
      stock: '11',
      status: 'In Stock',
      rating: 5.0,
      highlight: false
    },
    {
      product: 'EchoWave Bluetooth Speaker',
      price: '85.5',
      sales: '10',
      revenue: '855.00',
      stock: '20',
      status: 'In Stock',
      rating: 5.0,
      highlight: false
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Product</h1>
        </div>
        <div className="flex items-center space-x-3">
          <button className="p-2 text-gray-400 hover:text-gray-600">
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
          </button>
          <button className="p-2 text-gray-400 hover:text-gray-600">
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5v-5a7.5 7.5 0 1 0-15 0v5h5l-5 5-5-5h5V7a12 12 0 1 1 24 0v10z" />
            </svg>
          </button>
          <span className="text-sm text-gray-500">Customize Widget</span>
        </div>
      </div>

      {/* Statistics Cards */}
      <StatsGrid stats={stats} />

      {/* Product Table */}
      <DataTable
        title="Product"
        columns={productColumns}
        data={productData}
        searchable={true}
        filterable={true}
        sortable={true}
        selectable={true}
        actions={[
          {
            label: 'Add New Product',
            icon: PlusIcon,
            onClick: () => console.log('Add new product'),
            variant: 'primary'
          }
        ]}
        rowActions={[
          {
            label: 'View',
            icon: EyeIcon,
            onClick: (row) => console.log('View', row)
          },
          {
            label: 'Edit',
            icon: PencilIcon,
            onClick: (row) => console.log('Edit', row)
          },
          {
            label: 'Delete',
            icon: TrashIcon,
            onClick: (row) => console.log('Delete', row)
          }
        ]}
        pagination={{
          page: 1,
          pageSize: 10,
          total: 250,
          onPageChange: (page) => console.log('Page change:', page),
          onPageSizeChange: (pageSize) => console.log('Page size change:', pageSize)
        }}
      />
    </div>
  )
}
