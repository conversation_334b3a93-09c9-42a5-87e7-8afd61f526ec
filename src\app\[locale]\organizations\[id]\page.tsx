'use client'

import { useState, useEffect, use } from 'react'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import { useUser } from '@/components/auth/user-provider'
import { Button } from '@/components/ui/button'
import { supabase, type Organization, type Facility } from '@/lib/supabase'
import Link from 'next/link'
import toast from 'react-hot-toast'

export default function OrganizationDetailsPage({ 
  params 
}: { 
  params: Promise<{ locale: string; id: string }> 
}) {
  const { locale, id } = use(params)
  const t = useTranslations()
  const router = useRouter()
  const { user } = useUser()
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [facilities, setFacilities] = useState<Facility[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newFacility, setNewFacility] = useState({
    name: '',
    description: '',
    address: '',
  })
  const [creating, setCreating] = useState(false)

  useEffect(() => {
    if (user && id) {
      fetchData()
    }
  }, [user, id])

  const fetchData = async () => {
    try {
      // Fetch organization details
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', id)
        .single()

      if (orgError) {
        if (orgError.code === '42P01' || orgError.message.includes('relation') || orgError.message.includes('does not exist')) {
          console.warn('Organizations table not found. Database setup required.')
          router.push(`/${locale}/organizations`)
          return
        }
        throw orgError
      }

      if (!orgData) {
        router.push(`/${locale}/organizations`)
        return
      }

      setOrganization(orgData)

      // Fetch facilities for this organization
      const { data: facilitiesData, error: facilitiesError } = await supabase
        .from('facilities')
        .select('*')
        .eq('organization_id', id)
        .order('created_at', { ascending: false })

      if (facilitiesError) {
        if (facilitiesError.code === '42P01' || facilitiesError.message.includes('relation') || facilitiesError.message.includes('does not exist')) {
          console.warn('Facilities table not found. Database setup required.')
          setFacilities([])
          return
        }
        throw facilitiesError
      }

      setFacilities(facilitiesData || [])
    } catch (error) {
      console.warn('Error fetching data:', error)
      router.push(`/${locale}/organizations`)
    } finally {
      setLoading(false)
    }
  }

  const createFacility = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !newFacility.name.trim() || !organization) {
      toast.error('Please fill in all required fields and ensure you are logged in.')
      return
    }

    setCreating(true)
    try {
      const { error } = await supabase
        .from('facilities')
        .insert({
          name: newFacility.name.trim(),
          description: newFacility.description.trim() || null,
          address: newFacility.address.trim() || null,
          organization_id: organization.id,
          created_by: user.id,
        })

      if (error) {
        if (error.code === '42P01' || error.message.includes('relation') || error.message.includes('does not exist')) {
          toast.error('Database setup required. Please run the database setup script first. See DATABASE_SETUP.md for instructions.')
          setCreating(false)
          return
        }
        throw error
      }

      // Refresh facilities list
      await fetchData()

      // Reset form
      setNewFacility({
        name: '',
        description: '',
        address: '',
      })
      setShowCreateForm(false)

      toast.success('Facility created successfully!')
    } catch (error: any) {
      console.warn('Error creating facility:', error)
      toast.error(`Error creating facility: ${error.message || 'Please try again.'}`)
    } finally {
      setCreating(false)
    }
  }

  if (loading) {
    return <div className="text-center py-8">{t('common.loading')}</div>
  }

  if (!organization) {
    return <div className="text-center py-8">Organization not found</div>
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <div className="flex items-center space-x-2 mb-2">
            <Link 
              href={`/${locale}/organizations`}
              className="text-indigo-600 hover:text-indigo-500 text-sm font-medium"
            >
              ← {t('facilities.backToOrganizations')}
            </Link>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">
            {organization.name}
          </h1>
          {organization.description && (
            <p className="mt-2 text-sm text-gray-600">
              {organization.description}
            </p>
          )}
        </div>
        <Button
          onClick={() => setShowCreateForm(true)}
        >
          {t('facilities.createFacility')}
        </Button>
      </div>

      {/* Organization Details */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Organization Details
          </h2>
          <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt className="text-sm font-medium text-gray-500">Name</dt>
              <dd className="mt-1 text-sm text-gray-900">{organization.name}</dd>
            </div>
            {organization.description && (
              <div>
                <dt className="text-sm font-medium text-gray-500">Description</dt>
                <dd className="mt-1 text-sm text-gray-900">{organization.description}</dd>
              </div>
            )}
            <div>
              <dt className="text-sm font-medium text-gray-500">Created</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {new Date(organization.created_at).toLocaleDateString()}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      {/* Create Facility Form */}
      {showCreateForm && (
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            {t('facilities.createFacility')}
          </h2>
          <form onSubmit={createFacility} className="space-y-4">
            <div>
              <label htmlFor="facilityName" className="block text-sm font-medium text-gray-700">
                {t('facilities.facilityName')}
              </label>
              <input
                type="text"
                id="facilityName"
                required
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                value={newFacility.name}
                onChange={(e) => setNewFacility({ ...newFacility, name: e.target.value })}
              />
            </div>
            <div>
              <label htmlFor="facilityDescription" className="block text-sm font-medium text-gray-700">
                {t('facilities.description')}
              </label>
              <textarea
                id="facilityDescription"
                rows={3}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                value={newFacility.description}
                onChange={(e) => setNewFacility({ ...newFacility, description: e.target.value })}
              />
            </div>
            <div>
              <label htmlFor="facilityAddress" className="block text-sm font-medium text-gray-700">
                {t('facilities.address')}
              </label>
              <input
                type="text"
                id="facilityAddress"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                value={newFacility.address}
                onChange={(e) => setNewFacility({ ...newFacility, address: e.target.value })}
              />
            </div>
            <div className="flex space-x-3">
              <Button type="submit" disabled={creating}>
                {creating ? t('common.loading') : t('common.create')}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowCreateForm(false)
                  setNewFacility({
                    name: '',
                    description: '',
                    address: '',
                  })
                }}
              >
                {t('common.cancel')}
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* Facilities List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            {t('facilities.facilitiesInOrganization')}
          </h2>
          {facilities.length === 0 ? (
            <div className="text-center py-8">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 3h10M9 7h6m-6 4h6m-6 4h6"
                />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                {t('facilities.noFacilities')}
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating your first facility in this organization.
              </p>
              <div className="mt-6">
                <Button onClick={() => setShowCreateForm(true)}>
                  {t('facilities.createFacility')}
                </Button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {facilities.map((facility) => (
                <div
                  key={facility.id}
                  className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <h3 className="text-lg font-medium text-gray-900">{facility.name}</h3>
                  {facility.description && (
                    <p className="text-sm text-gray-600 mb-2">{facility.description}</p>
                  )}
                  {facility.address && (
                    <p className="text-sm text-gray-500 mb-4">{facility.address}</p>
                  )}
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">
                      {t('common.edit')}
                    </Button>
                    <Button size="sm" variant="outline">
                      {t('facilities.viewDetails')}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
