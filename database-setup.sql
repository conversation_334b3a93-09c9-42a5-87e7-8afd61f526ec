-- Database setup for Internal VE SaaS application
-- Run this in your Supabase SQL editor to create the necessary tables

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    account_number TEXT UNIQUE,
    job_duties TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_account_number ON public.users(account_number);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON public.users(is_active);

-- Create organizations table
CREATE TABLE IF NOT EXISTS public.organizations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    created_by <PERSON><PERSON><PERSON> REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create facilities table
CREATE TABLE IF NOT EXISTS public.facilities (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    organization_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    address TEXT,
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create workplaces table
CREATE TABLE IF NOT EXISTS public.workplaces (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    facility_id UUID REFERENCES public.facilities(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    location TEXT,
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create roles table
CREATE TABLE IF NOT EXISTS public.roles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create permissions table
CREATE TABLE IF NOT EXISTS public.permissions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    resource TEXT NOT NULL,
    action TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_roles table
CREATE TABLE IF NOT EXISTS public.user_roles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    organization_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE NOT NULL,
    facility_id UUID REFERENCES public.facilities(id) ON DELETE CASCADE,
    role_id UUID REFERENCES public.roles(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, organization_id, facility_id, role_id)
);

-- Create role_permissions table
CREATE TABLE IF NOT EXISTS public.role_permissions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    role_id UUID REFERENCES public.roles(id) ON DELETE CASCADE NOT NULL,
    permission_id UUID REFERENCES public.permissions(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(role_id, permission_id)
);

-- Insert default roles
INSERT INTO public.roles (name, description) VALUES
    ('Admin', 'Full access to all features and data'),
    ('Manager', 'Can manage facilities and users within organization'),
    ('Staff', 'Read-only access to assigned facilities')
ON CONFLICT (name) DO NOTHING;

-- Insert default permissions
INSERT INTO public.permissions (name, description, resource, action) VALUES
    ('read_organization', 'View organization details', 'organization', 'read'),
    ('update_organization', 'Update organization details', 'organization', 'update'),
    ('delete_organization', 'Delete organization', 'organization', 'delete'),
    ('create_facility', 'Create new facilities', 'facility', 'create'),
    ('read_facility', 'View facility details', 'facility', 'read'),
    ('update_facility', 'Update facility details', 'facility', 'update'),
    ('delete_facility', 'Delete facilities', 'facility', 'delete'),
    ('create_workplace', 'Create new workplaces', 'workplace', 'create'),
    ('read_workplace', 'View workplace details', 'workplace', 'read'),
    ('update_workplace', 'Update workplace details', 'workplace', 'update'),
    ('delete_workplace', 'Delete workplaces', 'workplace', 'delete'),
    ('invite_user', 'Invite new users', 'user', 'create'),
    ('read_user', 'View user details', 'user', 'read'),
    ('update_user', 'Update user details', 'user', 'update'),
    ('remove_user', 'Remove users', 'user', 'delete'),
    ('manage_roles', 'Assign and manage user roles', 'role', 'manage')
ON CONFLICT (name) DO NOTHING;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON public.organizations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_facilities_updated_at BEFORE UPDATE ON public.facilities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workplaces_updated_at BEFORE UPDATE ON public.workplaces
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.facilities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.workplaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;

-- Comprehensive RLS policies for users
-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Allow users to insert their own profile
CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Admins and managers can view users in their organizations
CREATE POLICY "Admins can view organization users" ON public.users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_roles ur1
            JOIN public.roles r1 ON ur1.role_id = r1.id
            JOIN public.user_roles ur2 ON ur1.organization_id = ur2.organization_id
            WHERE ur1.user_id = auth.uid()
            AND ur2.user_id = public.users.id
            AND r1.name IN ('Admin', 'Manager')
        )
    );

-- Admins can create users in their organizations
CREATE POLICY "Admins can create organization users" ON public.users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.user_roles ur
            JOIN public.roles r ON ur.role_id = r.id
            WHERE ur.user_id = auth.uid()
            AND r.name IN ('Admin', 'Manager')
        )
    );

-- Admins can update users in their organizations
CREATE POLICY "Admins can update organization users" ON public.users
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.user_roles ur1
            JOIN public.roles r1 ON ur1.role_id = r1.id
            JOIN public.user_roles ur2 ON ur1.organization_id = ur2.organization_id
            WHERE ur1.user_id = auth.uid()
            AND ur2.user_id = public.users.id
            AND r1.name IN ('Admin', 'Manager')
        )
    );

-- Everyone can read roles and permissions
CREATE POLICY "Everyone can read roles" ON public.roles
    FOR SELECT USING (true);

CREATE POLICY "Everyone can read permissions" ON public.permissions
    FOR SELECT USING (true);

CREATE POLICY "Everyone can read role_permissions" ON public.role_permissions
    FOR SELECT USING (true);

-- Comprehensive policies for organizations
CREATE POLICY "Users can read organizations they belong to" ON public.organizations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_roles
            WHERE user_id = auth.uid() AND organization_id = public.organizations.id
        )
    );

CREATE POLICY "Users can create organizations" ON public.organizations
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update organizations they belong to" ON public.organizations
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.user_roles
            WHERE user_id = auth.uid() AND organization_id = public.organizations.id
        )
    );

-- Comprehensive policies for facilities
CREATE POLICY "Users can read facilities in their organizations" ON public.facilities
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_roles
            WHERE user_id = auth.uid() AND organization_id = public.facilities.organization_id
        )
    );

CREATE POLICY "Users can create facilities in their organizations" ON public.facilities
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.user_roles
            WHERE user_id = auth.uid() AND organization_id = public.facilities.organization_id
        )
    );

CREATE POLICY "Users can update facilities in their organizations" ON public.facilities
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.user_roles
            WHERE user_id = auth.uid() AND organization_id = public.facilities.organization_id
        )
    );

CREATE POLICY "Users can delete facilities in their organizations" ON public.facilities
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.user_roles
            WHERE user_id = auth.uid() AND organization_id = public.facilities.organization_id
        )
    );

-- Comprehensive RLS policies for workplaces
CREATE POLICY "Users can read workplaces in their facilities" ON public.workplaces
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_roles ur
            JOIN public.facilities f ON f.organization_id = ur.organization_id
            WHERE ur.user_id = auth.uid() AND f.id = public.workplaces.facility_id
        )
    );

CREATE POLICY "Users can create workplaces in their facilities" ON public.workplaces
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.user_roles ur
            JOIN public.facilities f ON f.organization_id = ur.organization_id
            WHERE ur.user_id = auth.uid() AND f.id = public.workplaces.facility_id
        )
    );

CREATE POLICY "Users can update workplaces in their facilities" ON public.workplaces
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.user_roles ur
            JOIN public.facilities f ON f.organization_id = ur.organization_id
            WHERE ur.user_id = auth.uid() AND f.id = public.workplaces.facility_id
        )
    );

CREATE POLICY "Users can delete workplaces in their facilities" ON public.workplaces
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.user_roles ur
            JOIN public.facilities f ON f.organization_id = ur.organization_id
            WHERE ur.user_id = auth.uid() AND f.id = public.workplaces.facility_id
        )
    );

-- Basic policies for user_roles
CREATE POLICY "Users can read their own roles" ON public.user_roles
    FOR SELECT USING (user_id = auth.uid());
