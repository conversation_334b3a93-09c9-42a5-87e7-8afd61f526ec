# API Documentation

This document describes the API endpoints and data structures used in the Internal VE SaaS application.

## 🔗 Base URL

The application uses Supabase's auto-generated REST API:
```
https://hdompiwbxzymopmdhwbq.supabase.co/rest/v1/
```

## 🔐 Authentication

All API requests require authentication using Supabase Auth tokens.

### Headers
```
Authorization: Bearer <jwt_token>
apikey: <anon_key>
Content-Type: application/json
```

## 📊 Data Models

### User
```typescript
interface User {
  id: string              // UUID, references auth.users
  email: string           // User's email address
  full_name?: string      // User's full name
  avatar_url?: string     // Profile picture URL
  created_at: string      // ISO timestamp
  updated_at: string      // ISO timestamp
}
```

### Organization
```typescript
interface Organization {
  id: string              // UUID, primary key
  name: string            // Organization name
  description?: string    // Optional description
  created_by?: string     // UUID, references users.id
  created_at: string      // ISO timestamp
  updated_at: string      // ISO timestamp
}
```

### Facility
```typescript
interface Facility {
  id: string              // UUID, primary key
  organization_id: string // UUID, references organizations.id
  name: string            // Facility name
  description?: string    // Optional description
  address?: string        // Physical address
  created_by?: string     // UUID, references users.id
  created_at: string      // ISO timestamp
  updated_at: string      // ISO timestamp
}
```

### Role
```typescript
interface Role {
  id: string              // UUID, primary key
  name: string            // Role name (Admin, Manager, Staff)
  description?: string    // Role description
  created_at: string      // ISO timestamp
}
```

### Permission
```typescript
interface Permission {
  id: string              // UUID, primary key
  name: string            // Permission name
  description?: string    // Permission description
  resource: string        // Resource type (organization, facility, user)
  action: string          // Action type (create, read, update, delete)
  created_at: string      // ISO timestamp
}
```

### UserRole
```typescript
interface UserRole {
  id: string              // UUID, primary key
  user_id: string         // UUID, references users.id
  organization_id: string // UUID, references organizations.id
  facility_id?: string    // UUID, references facilities.id (optional)
  role_id: string         // UUID, references roles.id
  created_at: string      // ISO timestamp
}
```

## 🛠 API Endpoints

### Authentication

#### Sign Up
```http
POST /auth/v1/signup
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "data": {
    "full_name": "John Doe"
  }
}
```

#### Sign In
```http
POST /auth/v1/token?grant_type=password
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Sign Out
```http
POST /auth/v1/logout
Authorization: Bearer <jwt_token>
```

### Users

#### Get Current User
```http
GET /rest/v1/users?id=eq.<user_id>&select=*
Authorization: Bearer <jwt_token>
apikey: <anon_key>
```

#### Update User Profile
```http
PATCH /rest/v1/users?id=eq.<user_id>
Authorization: Bearer <jwt_token>
apikey: <anon_key>
Content-Type: application/json

{
  "full_name": "Updated Name",
  "avatar_url": "https://example.com/avatar.jpg"
}
```

### Organizations

#### List Organizations
```http
GET /rest/v1/organizations?select=*
Authorization: Bearer <jwt_token>
apikey: <anon_key>
```

#### Create Organization
```http
POST /rest/v1/organizations
Authorization: Bearer <jwt_token>
apikey: <anon_key>
Content-Type: application/json

{
  "name": "My Organization",
  "description": "Organization description",
  "created_by": "<user_id>"
}
```

#### Update Organization
```http
PATCH /rest/v1/organizations?id=eq.<org_id>
Authorization: Bearer <jwt_token>
apikey: <anon_key>
Content-Type: application/json

{
  "name": "Updated Organization Name",
  "description": "Updated description"
}
```

#### Delete Organization
```http
DELETE /rest/v1/organizations?id=eq.<org_id>
Authorization: Bearer <jwt_token>
apikey: <anon_key>
```

### Facilities

#### List Facilities
```http
GET /rest/v1/facilities?select=*,organization:organizations(*)
Authorization: Bearer <jwt_token>
apikey: <anon_key>
```

#### Create Facility
```http
POST /rest/v1/facilities
Authorization: Bearer <jwt_token>
apikey: <anon_key>
Content-Type: application/json

{
  "name": "Main Office",
  "description": "Primary office location",
  "address": "123 Main St, City, State",
  "organization_id": "<org_id>",
  "created_by": "<user_id>"
}
```

#### Update Facility
```http
PATCH /rest/v1/facilities?id=eq.<facility_id>
Authorization: Bearer <jwt_token>
apikey: <anon_key>
Content-Type: application/json

{
  "name": "Updated Facility Name",
  "address": "New Address"
}
```

### User Roles

#### List User Roles
```http
GET /rest/v1/user_roles?select=*,role:roles(*),organization:organizations(*),facility:facilities(*)
Authorization: Bearer <jwt_token>
apikey: <anon_key>
```

#### Assign Role to User
```http
POST /rest/v1/user_roles
Authorization: Bearer <jwt_token>
apikey: <anon_key>
Content-Type: application/json

{
  "user_id": "<user_id>",
  "organization_id": "<org_id>",
  "facility_id": "<facility_id>", // optional
  "role_id": "<role_id>"
}
```

### Roles and Permissions

#### List Roles
```http
GET /rest/v1/roles?select=*
Authorization: Bearer <jwt_token>
apikey: <anon_key>
```

#### List Permissions
```http
GET /rest/v1/permissions?select=*
Authorization: Bearer <jwt_token>
apikey: <anon_key>
```

#### Get Role Permissions
```http
GET /rest/v1/role_permissions?select=*,permission:permissions(*)&role_id=eq.<role_id>
Authorization: Bearer <jwt_token>
apikey: <anon_key>
```

## 🔒 Row Level Security (RLS)

All tables have RLS enabled with the following policies:

### Users Table
- Users can view and update their own profile
- Users can view other users in their organizations

### Organizations Table
- Users can view organizations they belong to
- Only organization admins can update organizations

### Facilities Table
- Users can view facilities in their organizations
- Admins and managers can create/update facilities

### User Roles Table
- Users can view their own roles
- Admins can manage user roles within their organizations

## 📝 Query Examples

### Get User's Organizations with Facilities
```http
GET /rest/v1/organizations?select=*,facilities(*)
Authorization: Bearer <jwt_token>
apikey: <anon_key>
```

### Get User's Permissions
```http
GET /rest/v1/user_roles?select=role:roles(role_permissions(permission:permissions(*)))&user_id=eq.<user_id>
Authorization: Bearer <jwt_token>
apikey: <anon_key>
```

### Filter Facilities by Organization
```http
GET /rest/v1/facilities?organization_id=eq.<org_id>&select=*
Authorization: Bearer <jwt_token>
apikey: <anon_key>
```

## ⚠️ Error Handling

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden (RLS policy violation)
- `404` - Not Found
- `409` - Conflict
- `500` - Internal Server Error

### Error Response Format
```json
{
  "code": "error_code",
  "message": "Human readable error message",
  "details": "Additional error details",
  "hint": "Suggestion for fixing the error"
}
```

## 🔄 Real-time Subscriptions

Supabase provides real-time subscriptions for live updates:

```javascript
// Subscribe to organization changes
const subscription = supabase
  .channel('organizations')
  .on('postgres_changes', 
    { event: '*', schema: 'public', table: 'organizations' },
    (payload) => {
      console.log('Change received!', payload)
    }
  )
  .subscribe()
```

## 📚 Additional Resources

- [Supabase API Documentation](https://supabase.com/docs/reference/api)
- [PostgREST API Documentation](https://postgrest.org/en/stable/api.html)
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript)

---

**API Version: 1.0**
