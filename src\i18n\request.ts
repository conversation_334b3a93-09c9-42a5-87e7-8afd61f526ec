import { getRequestConfig } from 'next-intl/server'
import enMessages from '../locales/en.json'
import ltMessages from '../locales/lt.json'

export const locales = ['en', 'lt'] as const
export type Locale = typeof locales[number]

export default getRequestConfig(async ({ locale }) => {
  // Validate locale
  if (!locale || !locales.includes(locale as any)) {
    locale = 'en'
  }

  const messages = locale === 'lt' ? ltMessages : enMessages

  return {
    locale,
    messages
  }
})
