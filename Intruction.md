Supabase project name: internal-ve (connected)
Github reposidory: https://github.com/AndriusAppDev/internal-ve.git (connected)


To build a scalable and extensible **SaaS application** using **Supabase**, **Next.js**, and **Tailwind CSS**, it's essential to start with a solid foundation that supports multi-tenancy (organizations), role-based access control (RBAC), and internationalization (i18n). Here's a **complete specification and development plan** for building the base application.

---

## ✅ Goal

Build a SaaS-ready application foundation using:

* **Next.js** (App Router)
* **Supabase** (auth, database, row-level security)
* **Tailwind CSS** (styling)
* **i18n** with **English** and **Lithuanian**
* Multi-tenant support: Organizations → Facilities → Users
* Role and Permission system (RBAC)
* Extensible architecture for future features

---

## 🧱 Core Functional Requirements

### 1. **Authentication (Supabase)**

* Email/password and OAuth support
* Magic link login (optional)
* Session management
* Email verification
* Reset password

### 2. **Organizations & Facilities**

* A user can **create** or **join** an organization
* An organization can have **multiple facilities**
* Facilities belong to **one organization**

### 3. **Role & Permission Management**

* Predefined roles: Admin, Manager, Staff (configurable)
* Each role has a set of permissions (CRUD actions)
* Assign users to roles within an organization/facility

### 4. **User Management**

* Admins can invite users to their organization
* Manage roles within facilities
* View users by organization/facility context

### 5. **Internationalization (i18n)**

* Default languages: English & Lithuanian
* Language switcher
* i18n for all UI text, date/time formatting

### 6. **Scalable Architecture**

* API routes for server-side logic
* Global state management for org/facility/user context
* Modular code for features (e.g. feature folders)

---

## 🧩 Project Structure (example)

```
/app
  /auth
  /dashboard
  /organization
  /facility
  /settings
  layout.tsx
  page.tsx
/lib
  supabase.ts
  auth.ts
  i18n.ts
  permissions.ts
/components
  /ui
  /auth
  /layout
/locales
  /en.json
  /lt.json
/middleware.ts
/tailwind.config.js
/supabase
  /schemas.sql
  /rls.sql
```

---

## 🛠 Supabase Configuration

### Tables:

* `users` (linked to Supabase `auth.users`)
* `organizations`
* `facilities`
* `roles` (e.g. Admin, Manager, Staff)
* `permissions` (CRUD actions, feature tags)
* `user_roles` (org\_id, user\_id, role\_id)
* `role_permissions` (role\_id, permission\_id)

### Row-Level Security:

* RLS policies to isolate organization/facility access
* Use Supabase `auth.uid()` for secure access control

---

## 🌍 Internationalization

Use [`next-intl`](https://next-intl-docs.vercel.app/) or [`next-i18next`](https://github.com/i18next/next-i18next).

* Locale detection middleware
* Language switcher component
* Translations stored in `/locales/en.json` and `/locales/lt.json`

---

## ⚙️ Recommended Tech Stack

| Feature          | Tech Used                |
| ---------------- | ------------------------ |
| Frontend         | Next.js (App Router)     |
| Styling          | Tailwind CSS             |
| Backend          | Supabase                 |
| Auth             | Supabase Auth            |
| Database         | Supabase (PostgreSQL)    |
| State Management | React Context / Zustand  |
| Translations     | next-intl / next-i18next |
| UI Components    | shadcn/ui (optional)     |

---

## 🔧 Setup Instructions

1. **Initialize Project**

   ```bash
   npx create-next-app@latest saas-app --app --typescript
   cd saas-app
   npm install @supabase/supabase-js next-intl tailwindcss postcss autoprefixer
   npx tailwindcss init -p
   ```

2. **Configure Supabase**

   * Set up project on [Supabase](https://app.supabase.com)
   * Create tables and RLS policies
   * Set env vars: `.env.local`

     ```
     NEXT_PUBLIC_SUPABASE_URL=
     NEXT_PUBLIC_SUPABASE_ANON_KEY=
     ```

3. **Implement Authentication**

   * Use Supabase Auth in `/app/auth` pages
   * Create login/register/reset flows
   * Add `useUser` context

4. **Organization Management**

   * Create `/organization` pages
   * Add org creation and user invitations
   * Manage facilities from dashboard

5. **RBAC Logic**

   * Role/permission seeding script
   * Utilities for checking permissions (`can('create_user')`)
   * Secure pages based on roles

6. **UI/UX**

   * Use Tailwind for layout
   * Add shadcn/ui or Radix UI if needed
   * Create a simple dashboard layout

7. **Translation Setup**

   * Setup `next-intl`
   * Add `en.json` and `lt.json`
   * Use `useTranslations()` hook in components

---

## 🌱 Future-Proofing the App

Prepare to add:

* Billing (Stripe integration)
* Audit logs
* Notifications (in-app/email)
* Feature toggles (per org/facility)
* Plug-and-play modules

---

## 📦 Optional Developer Tools

* ESLint + Prettier
* Storybook (for component dev)
* GitHub Actions for CI/CD
* Vercel or Railway for deployment

---

## 📘 Deliverables

1. Supabase schema SQL
2. RLS policy definitions
3. Base app with:

   * Auth flow
   * Org/Facility/Role management
   * Translations
   * UI structure

