{"name": "internal-ve", "version": "1.0.0", "description": "SaaS application with Next.js, Supabase, and Tailwind CSS", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "keywords": ["nextjs", "supabase", "saas", "tailwindcss"], "author": "", "license": "ISC", "dependencies": {"@heroicons/react": "^2.2.0", "@supabase/supabase-js": "^2.49.10", "@types/node": "^22.15.29", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "eslint": "^9.28.0", "eslint-config-next": "^15.3.3", "next": "^15.3.3", "next-intl": "^4.1.0", "postcss": "^8.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}