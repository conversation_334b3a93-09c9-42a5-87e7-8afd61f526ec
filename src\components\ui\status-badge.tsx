'use client'

interface StatusBadgeProps {
  status: string
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info'
  size?: 'sm' | 'md' | 'lg'
}

export function StatusBadge({ status, variant = 'default', size = 'md' }: StatusBadgeProps) {
  const getVariantClasses = () => {
    switch (variant) {
      case 'success':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'danger':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'info':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-0.5 text-xs'
      case 'lg':
        return 'px-3 py-1 text-sm'
      default:
        return 'px-2.5 py-0.5 text-xs'
    }
  }

  // Auto-detect variant based on status text
  const getAutoVariant = () => {
    const statusLower = status.toLowerCase()
    if (statusLower.includes('in stock') || statusLower.includes('active') || statusLower.includes('success')) {
      return 'success'
    }
    if (statusLower.includes('out of stock') || statusLower.includes('inactive') || statusLower.includes('error')) {
      return 'danger'
    }
    if (statusLower.includes('restock') || statusLower.includes('pending') || statusLower.includes('warning')) {
      return 'warning'
    }
    return variant
  }

  const finalVariant = variant === 'default' ? getAutoVariant() : variant

  return (
    <span
      className={`inline-flex items-center rounded-full border font-medium ${getSizeClasses()} ${getVariantClasses()}`}
    >
      {status}
    </span>
  )
}

// Predefined status badges for common use cases
export function InStockBadge() {
  return <StatusBadge status="In Stock" variant="success" />
}

export function OutOfStockBadge() {
  return <StatusBadge status="Out of Stock" variant="danger" />
}

export function RestockBadge() {
  return <StatusBadge status="Restock" variant="warning" />
}

export function ActiveBadge() {
  return <StatusBadge status="Active" variant="success" />
}

export function InactiveBadge() {
  return <StatusBadge status="Inactive" variant="danger" />
}
