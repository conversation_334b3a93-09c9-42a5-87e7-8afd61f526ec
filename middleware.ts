import createMiddleware from 'next-intl/middleware'
import { locales } from './src/lib/i18n'

export default createMiddleware({
  // A list of all locales that are supported
  locales,

  // Used when no locale matches
  defaultLocale: 'en',

  // Always use locale prefix
  localePrefix: 'always'
})

export const config = {
  // Match only internationalized pathnames
  matcher: ['/', '/(en|lt)/:path*', '/((?!api|_next|_vercel|.*\\..*).*)']
}
