'use client'

import { useState } from 'react'
import { supabase } from '@/lib/supabase'
import { authService } from '@/lib/auth'

export default function AuthTestPage() {
  const [result, setResult] = useState<string>('')
  const [loading, setLoading] = useState(false)

  const testConnection = async () => {
    setLoading(true)
    setResult('Testing connection...\n')
    
    try {
      // Test 1: Basic connection
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession()
      if (sessionError) {
        setResult(prev => prev + `❌ Session error: ${sessionError.message}\n`)
        return
      }
      setResult(prev => prev + `✅ Connection successful\n`)
      setResult(prev => prev + `Current session: ${sessionData.session ? 'Active' : 'None'}\n`)

      // Test 2: Test sign in
      const testEmail = '<EMAIL>'
      const testPassword = 'testpassword123'
      
      setResult(prev => prev + `\nTesting sign in with ${testEmail}...\n`)
      
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: testEmail,
        password: testPassword,
      })
      
      if (signInError) {
        setResult(prev => prev + `❌ Sign in error: ${signInError.message}\n`)
      } else {
        setResult(prev => prev + `✅ Sign in successful: ${signInData.user?.email}\n`)
        setResult(prev => prev + `Access token: ${signInData.session?.access_token ? 'Present' : 'Missing'}\n`)
        
        // Test 3: Get current user
        setResult(prev => prev + `\nTesting getCurrentUser...\n`)
        const currentUser = await authService.getCurrentUser()
        if (currentUser) {
          setResult(prev => prev + `✅ getCurrentUser successful: ${currentUser.email}\n`)
        } else {
          setResult(prev => prev + `❌ getCurrentUser returned null\n`)
        }
      }
      
    } catch (error: any) {
      setResult(prev => prev + `❌ Test failed: ${error.message}\n`)
    } finally {
      setLoading(false)
    }
  }

  const testSignOut = async () => {
    setLoading(true)
    try {
      await supabase.auth.signOut()
      setResult(prev => prev + `\n✅ Sign out successful\n`)
    } catch (error: any) {
      setResult(prev => prev + `\n❌ Sign out error: ${error.message}\n`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Authentication Test</h1>
        
        <div className="bg-white shadow rounded-lg p-6 mb-6">
          <div className="flex space-x-4 mb-4">
            <button
              onClick={testConnection}
              disabled={loading}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test Authentication'}
            </button>
            
            <button
              onClick={testSignOut}
              disabled={loading}
              className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 disabled:opacity-50"
            >
              Test Sign Out
            </button>
          </div>
          
          <div className="bg-gray-100 p-4 rounded">
            <h3 className="font-medium mb-2">Test Results:</h3>
            <pre className="text-sm whitespace-pre-wrap">{result || 'Click "Test Authentication" to start'}</pre>
          </div>
        </div>
        
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Environment Check</h2>
          <div className="space-y-2 text-sm">
            <div>
              <strong>Supabase URL:</strong> {process.env.NEXT_PUBLIC_SUPABASE_URL || 'Not set'}
            </div>
            <div>
              <strong>Supabase Anon Key:</strong> {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set'}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
