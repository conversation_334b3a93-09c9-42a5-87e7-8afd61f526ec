'use client'

import { usePathname, useRouter } from 'next/navigation'
import { locales } from '@/lib/i18n'

export function LanguageSwitcher() {
  const pathname = usePathname()
  const router = useRouter()

  const switchLanguage = (newLocale: string) => {
    // Replace the locale in the current pathname
    const segments = pathname.split('/')
    segments[1] = newLocale
    const newPath = segments.join('/')
    router.push(newPath)
  }

  const currentLocale = pathname.split('/')[1]

  return (
    <div className="relative inline-block text-left">
      <select
        value={currentLocale}
        onChange={(e) => switchLanguage(e.target.value)}
        className="block w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
      >
        {locales.map((locale) => (
          <option key={locale} value={locale}>
            {locale === 'en' ? 'English' : 'Lietuvių'}
          </option>
        ))}
      </select>
    </div>
  )
}
