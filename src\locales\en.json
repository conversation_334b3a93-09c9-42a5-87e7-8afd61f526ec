{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "actions": "Actions", "yes": "Yes", "no": "No", "confirm": "Confirm", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "createdDate": "Created Date"}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "fullName": "Full Name", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "signInWithEmail": "Sign in with email", "signUpWithEmail": "Sign up with email", "checkEmail": "Check your email for the confirmation link", "passwordResetSent": "Password reset email sent", "invalidCredentials": "Invalid email or password", "emailRequired": "Email is required", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 6 characters", "passwordsDoNotMatch": "Passwords do not match", "loading": "Loading..."}, "navigation": {"dashboard": "Dashboard", "organizations": "Organizations", "facilities": "Facilities", "users": "Users", "settings": "Settings", "profile": "Profile"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome back!", "overview": "Overview", "recentActivity": "Recent Activity", "quickActions": "Quick Actions"}, "organizations": {"title": "Organizations", "createOrganization": "Create Organization", "organizationName": "Organization Name", "description": "Description", "noOrganizations": "No organizations found", "organizationCreated": "Organization created successfully", "organizationUpdated": "Organization updated successfully", "organizationDeleted": "Organization deleted successfully"}, "facilities": {"title": "Facilities", "createFacility": "Create Facility", "facilityName": "Facility Name", "description": "Description", "address": "Address", "noFacilities": "No facilities found", "facilityCreated": "Facility created successfully", "facilityUpdated": "Facility updated successfully", "facilityDeleted": "Facility deleted successfully", "viewDetails": "View Details", "backToOrganizations": "Back to Organizations", "facilitiesInOrganization": "Facilities in this organization", "facilitiesCount": "Facilities Count"}, "workplaces": {"title": "Workplaces", "workplace": "Workplace", "workplaces": "Workplaces", "createWorkplace": "Create Workplace", "workplaceName": "Workplace Name", "description": "Description", "location": "Location", "noWorkplaces": "No workplaces found", "workplaceCreated": "Workplace created successfully", "workplaceUpdated": "Workplace updated successfully", "workplaceDeleted": "Workplace deleted successfully", "manageWorkplaces": "Manage Workplaces", "workplacesInFacility": "Workplaces in this facility"}, "settings": {"title": "Settings", "organizationSettings": "Organization Settings", "facilitySettings": "Facility Settings", "workplaceSettings": "Workplace Settings", "manageFacilities": "Manage Facilities", "manageWorkplaces": "Manage Workplaces"}, "users": {"title": "Users", "inviteUser": "Invite User", "createUser": "Create User", "userEmail": "User Email", "userFullName": "Full Name", "accountNumber": "Account Number", "jobDuties": "Job Duties", "selectOrganization": "Select Organization", "temporaryPassword": "Temporary Password", "copyPassword": "Copy Password", "passwordCopied": "Password copied to clipboard", "role": "Role", "status": "Status", "active": "Active", "inactive": "Inactive", "enable": "Enable", "disable": "Disable", "resetPassword": "Reset Password", "editUser": "Edit User", "noUsers": "No users found", "userCreated": "User created successfully", "userInvited": "User invited successfully", "userUpdated": "User updated successfully", "userRemoved": "User removed successfully", "userEnabled": "User enabled successfully", "userDisabled": "User disabled successfully", "passwordReset": "Password reset successfully", "userManagement": "User Management", "manageUsers": "Manage Users", "organization": "Organization"}, "roles": {"admin": "Admin", "manager": "Manager", "staff": "Staff"}}