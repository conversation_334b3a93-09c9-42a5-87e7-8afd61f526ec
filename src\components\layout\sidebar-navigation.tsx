'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useTranslations } from 'next-intl'
import {
  HomeIcon,
  BuildingOfficeIcon,
  UsersIcon,
  Cog6ToothIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'

interface SidebarNavigationProps {
  locale: string
  sidebarOpen: boolean
  setSidebarOpen: (open: boolean) => void
}

export function SidebarNavigation({
  locale,
  sidebarOpen,
  setSidebarOpen
}: SidebarNavigationProps) {
  const t = useTranslations()
  const pathname = usePathname()

  const navigation = [
    { 
      name: t('navigation.dashboard'), 
      href: `/${locale}/dashboard`, 
      icon: HomeIcon,
      current: pathname === `/${locale}/dashboard`
    },
    { 
      name: t('navigation.organizations'), 
      href: `/${locale}/organizations`, 
      icon: BuildingOfficeIcon,
      current: pathname.startsWith(`/${locale}/organizations`)
    },
    { 
      name: t('navigation.users'), 
      href: `/${locale}/users`, 
      icon: UsersIcon,
      current: pathname.startsWith(`/${locale}/users`)
    },
    { 
      name: t('navigation.settings'), 
      href: `/${locale}/settings`, 
      icon: Cog6ToothIcon,
      current: pathname.startsWith(`/${locale}/settings`)
    },
  ]

  const handleLinkClick = () => {
    setSidebarOpen(false)
  }

  return (
    <>
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-gray-900">
          <div className="flex h-16 shrink-0 items-center justify-between px-6">
            <div className="flex items-center">
              <div className="flex h-8 w-8 items-center justify-center rounded bg-indigo-600 text-white font-bold text-sm">
                IV
              </div>
              <div className="ml-3">
                <div className="text-white font-semibold text-sm">Internal VE</div>
                <div className="text-gray-400 text-xs">SaaS Platform</div>
              </div>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-900 rounded-md"
              aria-label="Close sidebar"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Mobile navigation content */}
          <nav className="flex flex-1 flex-col mt-5">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <div className="text-xs font-semibold leading-6 text-gray-400 uppercase tracking-wide px-6">
                  MAIN MENU
                </div>
                <ul role="list" className="mt-2 space-y-1 px-6">
                  {navigation.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        onClick={handleLinkClick}
                        className={`${
                          item.current
                            ? 'bg-gray-800 text-white'
                            : 'text-gray-300 hover:text-white hover:bg-gray-800'
                        } group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-medium focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-900`}
                      >
                        <item.icon className="h-5 w-5 shrink-0" />
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-40 lg:flex lg:w-64 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900 px-6 pb-4">
          {/* Logo */}
          <div className="flex h-16 shrink-0 items-center">
            <div className="flex items-center">
              <div className="flex h-8 w-8 items-center justify-center rounded bg-indigo-600 text-white font-bold text-sm">
                IV
              </div>
              <div className="ml-3">
                <div className="text-white font-semibold text-sm">Internal VE</div>
                <div className="text-gray-400 text-xs">SaaS Platform</div>
              </div>
            </div>
          </div>

          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              {/* Main Menu */}
              <li>
                <div className="text-xs font-semibold leading-6 text-gray-400 uppercase tracking-wide">
                  MAIN MENU
                </div>
                <ul role="list" className="mt-2 space-y-1">
                  {navigation.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className={`${
                          item.current
                            ? 'bg-gray-800 text-white'
                            : 'text-gray-300 hover:text-white hover:bg-gray-800'
                        } group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-medium focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-900`}
                      >
                        <item.icon className="h-5 w-5 shrink-0" />
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </>
  )
}
