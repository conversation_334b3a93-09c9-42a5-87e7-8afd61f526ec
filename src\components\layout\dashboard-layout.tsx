'use client'

import { useEffect } from 'react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { useUser } from '@/components/auth/user-provider'
import { Button } from '@/components/ui/button'
import { LanguageSwitcher } from './language-switcher'

interface DashboardLayoutProps {
  children: React.ReactNode
  locale: string
}

export function DashboardLayout({ children, locale }: DashboardLayoutProps) {
  const t = useTranslations()
  const { user, signOut, loading } = useUser()
  const pathname = usePathname()
  const router = useRouter()

  const handleSignOut = async () => {
    await signOut()
    router.push(`/${locale}/auth/signin`)
  }

  // Handle redirect for unauthenticated users in useEffect
  useEffect(() => {
    if (!loading && !user) {
      router.push(`/${locale}/auth/signin`)
    }
  }, [loading, user, router, locale])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">{t('common.loading')}</div>
      </div>
    )
  }

  if (!user) {
    // Don't render anything while redirecting
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">{t('common.loading')}</div>
      </div>
    )
  }

  const navigation = [
    { name: t('navigation.dashboard'), href: `/${locale}/dashboard` },
    { name: t('navigation.organizations'), href: `/${locale}/organizations` },
    { name: t('navigation.users'), href: `/${locale}/users` },
    { name: t('navigation.settings'), href: `/${locale}/settings` },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <h1 className="text-xl font-bold text-gray-900">Internal VE</h1>
              </div>
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`${
                      pathname === item.href
                        ? 'border-indigo-500 text-gray-900'
                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                    } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
                  >
                    {item.name}
                  </Link>
                ))}
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <LanguageSwitcher />
              <span className="text-sm text-gray-700">
                {user.full_name || user.email}
              </span>
              <Button variant="outline" size="sm" onClick={handleSignOut}>
                {t('auth.signOut')}
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {children}
        </div>
      </main>
    </div>
  )
}
