# Development Guide

This guide covers the development workflow, architecture decisions, and best practices for the Internal VE SaaS application.

## 🏗 Architecture Overview

### Frontend Architecture
- **Next.js 15** with App Router for modern React development
- **TypeScript** for type safety and better developer experience
- **Tailwind CSS** for utility-first styling
- **next-intl** for internationalization

### Backend Architecture
- **Supabase** for authentication, database, and real-time features
- **PostgreSQL** with Row-Level Security (RLS) for data isolation
- **RESTful API** through Supabase auto-generated APIs

### State Management
- **React Context** for authentication state
- **Server Components** for data fetching
- **Client Components** for interactive features

## 🛠 Development Setup

### Prerequisites
- Node.js 18+
- Git
- Code editor (VS Code recommended)

### Recommended VS Code Extensions
- ES7+ React/Redux/React-Native snippets
- Tailwind CSS IntelliSense
- TypeScript Importer
- Prettier - Code formatter
- ESLint

### Environment Setup
1. Clone the repository
2. Install dependencies: `npm install`
3. Copy environment variables (already configured)
4. Start development server: `npm run dev`

## 📁 Project Structure Deep Dive

### App Router Structure
```
src/app/
├── [locale]/                 # Internationalized routes
│   ├── auth/                 # Authentication flows
│   │   ├── signin/           # Sign in page
│   │   └── signup/           # Sign up page
│   ├── dashboard/            # Main application
│   ├── organizations/        # Organization management
│   ├── facilities/           # Facility management
│   ├── users/                # User management
│   └── settings/             # Application settings
├── globals.css               # Global styles and CSS variables
├── layout.tsx                # Root layout
└── page.tsx                  # Root page (redirects to auth)
```

### Component Organization
```
src/components/
├── auth/                     # Authentication-related components
│   └── user-provider.tsx     # User context provider
├── layout/                   # Layout components
│   ├── dashboard-layout.tsx  # Main dashboard layout
│   └── language-switcher.tsx # Language switching component
└── ui/                       # Reusable UI components
    └── button.tsx            # Button component
```

### Library Structure
```
src/lib/
├── auth.ts                   # Authentication utilities and types
├── supabase.ts               # Supabase client and database types
├── i18n.ts                   # Internationalization configuration
└── utils.ts                  # General utility functions
```

## 🔐 Database Schema Details

### Core Tables

#### users
- Extends Supabase auth.users
- Stores additional profile information
- Links to user_roles for permissions

#### organizations
- Top-level tenant entities
- Has many facilities
- Has many users through user_roles

#### facilities
- Belongs to one organization
- Can have multiple users assigned
- Represents physical or logical locations

#### roles
- Predefined system roles (Admin, Manager, Staff)
- Has many permissions through role_permissions
- Assigned to users through user_roles

#### permissions
- Granular permissions for specific actions
- Organized by resource and action
- Assigned to roles through role_permissions

### RLS Policies
- Users can only see data within their organizations
- Admins have full access within their organizations
- Managers can manage facilities and users
- Staff have read-only access

## 🎨 Styling Guidelines

### Tailwind CSS Usage
- Use utility classes for styling
- Follow mobile-first responsive design
- Use CSS custom properties for theming
- Maintain consistent spacing and typography

### Color Scheme
- Primary: Indigo (indigo-600, indigo-500)
- Secondary: Gray scale for text and backgrounds
- Success: Green for positive actions
- Error: Red for destructive actions
- Warning: Yellow for caution

### Component Patterns
- Use consistent button variants (primary, secondary, outline)
- Maintain consistent form styling
- Use consistent spacing patterns (4, 6, 8, 12, 16, 24)

## 🔧 Development Workflow

### Git Workflow
1. Create feature branch from `main`
2. Make changes with descriptive commits
3. Test thoroughly
4. Create pull request
5. Code review and merge

### Commit Message Format
```
type(scope): description

Examples:
feat(auth): add password reset functionality
fix(dashboard): resolve navigation issue
docs(readme): update installation instructions
```

### Testing Strategy
- Manual testing for UI components
- Database testing through Supabase dashboard
- Authentication flow testing
- Permission testing for different roles

## 🌍 Internationalization

### Adding New Languages
1. Create new locale file in `src/locales/`
2. Add locale to `src/lib/i18n.ts`
3. Update middleware matcher
4. Test all pages with new locale

### Translation Keys
- Use nested objects for organization
- Keep keys descriptive and consistent
- Include context in key names when needed

### Best Practices
- Always use translation keys, never hardcoded strings
- Provide fallbacks for missing translations
- Test with longer text (German, Finnish) for layout issues

## 🔒 Security Considerations

### Authentication
- Use Supabase Auth for secure authentication
- Implement proper session management
- Handle authentication errors gracefully

### Authorization
- Rely on RLS policies for data access control
- Validate permissions on both client and server
- Never trust client-side permission checks alone

### Data Protection
- Use HTTPS in production
- Sanitize user inputs
- Follow GDPR guidelines for user data

## 📊 Performance Optimization

### Next.js Optimizations
- Use Server Components when possible
- Implement proper loading states
- Optimize images with Next.js Image component
- Use dynamic imports for code splitting

### Database Optimizations
- Use proper indexes on frequently queried columns
- Implement pagination for large datasets
- Use Supabase real-time features judiciously

### Bundle Optimization
- Analyze bundle size with `npm run build`
- Remove unused dependencies
- Use tree shaking effectively

## 🐛 Debugging

### Common Issues
1. **Authentication errors**: Check Supabase configuration
2. **Permission errors**: Verify RLS policies
3. **Build errors**: Check TypeScript types
4. **Styling issues**: Verify Tailwind configuration

### Debugging Tools
- React Developer Tools
- Supabase Dashboard for database queries
- Network tab for API calls
- Console for JavaScript errors

### Logging
- Use `console.log` for development
- Implement proper error logging for production
- Use Supabase logs for database issues

## 🚀 Adding New Features

### Feature Development Process
1. **Plan**: Define requirements and user stories
2. **Design**: Create UI mockups and database schema changes
3. **Implement**: Write code following existing patterns
4. **Test**: Thoroughly test the feature
5. **Document**: Update documentation and README

### Database Changes
1. Plan schema changes carefully
2. Create migration scripts
3. Update TypeScript types
4. Test with existing data

### UI Components
1. Follow existing design patterns
2. Make components reusable
3. Add proper TypeScript types
4. Test responsive behavior

## 📚 Resources

### Documentation
- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs)

### Learning Resources
- [Next.js Learn](https://nextjs.org/learn)
- [Supabase Tutorials](https://supabase.com/docs/guides/getting-started)
- [React Documentation](https://react.dev)

---

**Happy Coding! 💻**
