'use client'

import { StarIcon } from '@heroicons/react/24/solid'
import { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline'

interface RatingProps {
  rating: number
  maxRating?: number
  size?: 'sm' | 'md' | 'lg'
  showValue?: boolean
  readonly?: boolean
  onChange?: (rating: number) => void
}

export function Rating({ 
  rating, 
  maxRating = 5, 
  size = 'md', 
  showValue = true, 
  readonly = true,
  onChange 
}: RatingProps) {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-3 w-3'
      case 'lg':
        return 'h-6 w-6'
      default:
        return 'h-4 w-4'
    }
  }

  const getTextSize = () => {
    switch (size) {
      case 'sm':
        return 'text-xs'
      case 'lg':
        return 'text-base'
      default:
        return 'text-sm'
    }
  }

  const handleStarClick = (starRating: number) => {
    if (!readonly && onChange) {
      onChange(starRating)
    }
  }

  return (
    <div className="flex items-center space-x-1">
      <div className="flex items-center">
        {Array.from({ length: maxRating }, (_, index) => {
          const starValue = index + 1
          const isFilled = starValue <= rating
          const isHalfFilled = starValue - 0.5 <= rating && starValue > rating

          return (
            <button
              key={index}
              type="button"
              onClick={() => handleStarClick(starValue)}
              disabled={readonly}
              className={`${readonly ? 'cursor-default' : 'cursor-pointer hover:scale-110'} transition-transform`}
            >
              {isFilled ? (
                <StarIcon className={`${getSizeClasses()} text-yellow-400`} />
              ) : isHalfFilled ? (
                <div className="relative">
                  <StarOutlineIcon className={`${getSizeClasses()} text-gray-300`} />
                  <div className="absolute inset-0 overflow-hidden" style={{ width: '50%' }}>
                    <StarIcon className={`${getSizeClasses()} text-yellow-400`} />
                  </div>
                </div>
              ) : (
                <StarOutlineIcon className={`${getSizeClasses()} text-gray-300`} />
              )}
            </button>
          )
        })}
      </div>
      {showValue && (
        <span className={`${getTextSize()} text-gray-600 font-medium ml-1`}>
          {rating.toFixed(1)}
        </span>
      )}
    </div>
  )
}
