# Vercel Deployment Guide

This guide will help you fix the "Application error: a client-side exception has occurred" error when deploying to Vercel.

## 🚨 The Problem

The error occurs because Vercel doesn't have access to your Supabase environment variables. The `.env.local` file is not pushed to GitHub (correctly for security), so Vercel can't access the Supabase credentials.

## ✅ The Solution

### Step 1: Add Environment Variables in Vercel

1. **Go to Vercel Dashboard**:
   - Visit [vercel.com](https://vercel.com)
   - Sign in and find your `internal-ve` project

2. **Navigate to Settings**:
   - Click on your project name
   - Go to the **Settings** tab
   - Click **Environment Variables** in the left sidebar

3. **Add Environment Variables**:

   **Variable 1:**
   - **Name**: `NEXT_PUBLIC_SUPABASE_URL`
   - **Value**: `https://hdompiwbxzymopmdhwbq.supabase.co`
   - **Environments**: Check all boxes (Production, Preview, Development)
   - Click **Save**

   **Variable 2:**
   - **Name**: `NEXT_PUBLIC_SUPABASE_ANON_KEY`
   - **Value**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhkb21waXdieHp5bW9wbWRod2JxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxMzUzODAsImV4cCI6MjA2NDcxMTM4MH0._GkTn1BtWRz0T3fNlBNBm2c5ezw3gGc40bzn8tq8JRw`
   - **Environments**: Check all boxes (Production, Preview, Development)
   - Click **Save**

### Step 2: Redeploy Your Application

After adding the environment variables, you need to trigger a new deployment:

**Option A: Redeploy from Vercel Dashboard**
1. Go to the **Deployments** tab in your Vercel project
2. Find your latest deployment
3. Click the **three dots (⋯)** next to it
4. Select **Redeploy**
5. Confirm the redeploy

**Option B: Automatic Redeploy (Already Done)**
- The latest commit to main branch will automatically trigger a new deployment
- This deployment will include the environment variables you just added

### Step 3: Verify the Fix

1. **Wait for deployment to complete** (usually 1-2 minutes)
2. **Visit your Vercel URL** (e.g., `https://internal-ve.vercel.app`)
3. **Check that the app loads** without the "Application error" message
4. **Test basic functionality**:
   - Sign up/Sign in should work
   - Navigation should work
   - No console errors related to Supabase

## 🔍 Troubleshooting

### If you still see errors:

1. **Check Environment Variables**:
   - Go back to Vercel Settings > Environment Variables
   - Verify both variables are present and correct
   - Make sure they're enabled for all environments

2. **Check Browser Console**:
   - Open browser developer tools (F12)
   - Look for specific error messages
   - Common issues: typos in environment variable names or values

3. **Verify Supabase Connection**:
   - Test the Supabase URL in your browser: `https://hdompiwbxzymopmdhwbq.supabase.co`
   - Should show a Supabase API response

4. **Force Redeploy**:
   - Sometimes you need to redeploy twice for environment variables to take effect
   - Try redeploying again from the Vercel dashboard

## 📋 Environment Variables Reference

Your application needs these exact environment variables:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://hdompiwbxzymopmdhwbq.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhkb21waXdieHp5bW9wbWRod2JxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxMzUzODAsImV4cCI6MjA2NDcxMTM4MH0._GkTn1BtWRz0T3fNlBNBm2c5ezw3gGc40bzn8tq8JRw
```

## 🎯 Expected Result

After following these steps:
- ✅ Your Vercel app should load without errors
- ✅ Authentication (sign up/sign in) should work
- ✅ All pages should be accessible
- ✅ No "Application error" messages

## 🚀 Next Steps

Once your app is deployed successfully:
1. **Set up the database** using `database-setup.sql` (see `DATABASE_SETUP.md`)
2. **Test organization and facility creation**
3. **Invite team members** to test the application

## 📞 Support

If you continue to experience issues:
1. Check the Vercel deployment logs for specific error messages
2. Verify your Supabase project is active and accessible
3. Ensure all environment variables are correctly set

The deployment should work perfectly after adding the environment variables!
